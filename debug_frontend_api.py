#!/usr/bin/env python3

import requests
import json

def debug_frontend_api():
    """Debug what the frontend is receiving from APIs"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        print('🔍 DEBUGGING FRONTEND API RESPONSES')
        print('=' * 50)
        
        # Test what frontend gets for diamonds
        print('\n1. Diamonds API Response (what frontend receives):')
        response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f'   Status: {response.status_code}')
            print(f'   Response keys: {list(data.keys())}')
            
            diamonds = data.get('data', [])
            print(f'   Total diamonds: {len(diamonds)}')
            
            # Show first few diamonds in detail
            for i, diamond in enumerate(diamonds[:3]):
                print(f'\n   Diamond {i+1}:')
                print(f'   • ID: {diamond.get("id")}')
                print(f'   • Shape: {diamond.get("shape")}')
                print(f'   • Carat: {diamond.get("carat")}')
                print(f'   • Color: {diamond.get("color")}')
                print(f'   • Clarity: {diamond.get("clarity")}')
                print(f'   • Quantity: {diamond.get("quantity")}')
                print(f'   • Certificate: {diamond.get("certificate_no")}')
                print(f'   • Status: {diamond.get("status")}')
                
                # Check if this would create a valid option
                if diamond.get('quantity', 0) > 0:
                    option_label = f"{diamond.get('shape')} - {diamond.get('carat')}ct ({diamond.get('quantity')} available)"
                    print(f'   • Option label: "{option_label}"')
                else:
                    print(f'   • ❌ Not available (quantity: {diamond.get("quantity")})')
        else:
            print(f'   ❌ Failed: {response.status_code}')
            print(f'   Error: {response.text}')
        
        # Test vendors API
        print('\n2. Vendors API Response:')
        response = requests.get(f'{BASE_URL}/vendors', headers=headers)
        if response.status_code == 200:
            vendors = response.json()
            print(f'   Status: {response.status_code}')
            print(f'   Total vendors: {len(vendors)}')
            
            for i, vendor in enumerate(vendors[:2]):
                print(f'   Vendor {i+1}: ID {vendor.get("id")}, Name: "{vendor.get("name")}"')
        else:
            print(f'   ❌ Failed: {response.status_code}')
        
        # Test manufacturing list API
        print('\n3. Manufacturing List API Response:')
        response = requests.get(f'{BASE_URL}/manufacturing', headers=headers)
        if response.status_code == 200:
            manufacturing_data = response.json()
            print(f'   Status: {response.status_code}')
            print(f'   Response type: {type(manufacturing_data)}')
            
            if isinstance(manufacturing_data, list):
                print(f'   Total requests: {len(manufacturing_data)}')
                if manufacturing_data:
                    sample = manufacturing_data[0]
                    print(f'   Sample request keys: {list(sample.keys())[:10]}...')
                    print(f'   Sample diamonds: {len(sample.get("diamonds", []))}')
            else:
                print(f'   Response: {manufacturing_data}')
        else:
            print(f'   ❌ Failed: {response.status_code}')
        
        print('\n🎯 EXPECTED FRONTEND BEHAVIOR:')
        print('   1. Diamond dropdown should show options like "Princess - 1.25ct (2 available)"')
        print('   2. Only diamonds with quantity > 0 should appear')
        print('   3. Manufacturing list should show as array directly')
        print('   4. Vendors should show in dropdown')
        
        print('\n🔧 TROUBLESHOOTING:')
        print('   1. Check browser console for JavaScript errors')
        print('   2. Verify network requests in browser dev tools')
        print('   3. Check if frontend is making correct API calls')
        print('   4. Restart backend server to get diamond details in manufacturing')
        
    else:
        print(f'❌ Login failed: {response.status_code}')

if __name__ == '__main__':
    debug_frontend_api()
