#!/usr/bin/env python3

import requests
import json

def test_frontend_diamond_selection():
    """Test frontend diamond selection functionality"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        print('🔧 TESTING FRONTEND DIAMOND SELECTION')
        print('=' * 50)
        
        # Test 1: Check if diamonds API works for frontend
        print('\n1. Testing Diamonds API for Frontend...')
        response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f'   ✅ Diamonds API working: {response.status_code}')
            print(f'   Response structure: {list(data.keys())}')
            diamonds = data.get('data', [])
            print(f'   Available diamonds: {len(diamonds)}')
            
            if diamonds:
                print(f'   Sample diamond structure:')
                sample = diamonds[0]
                print(f'   • ID: {sample.get("id")}')
                print(f'   • Shape: {sample.get("shape")}')
                print(f'   • Carat: {sample.get("carat")}')
                print(f'   • Quantity: {sample.get("quantity")}')
                print(f'   • Certificate: {sample.get("certificate_no")}')
        else:
            print(f'   ❌ Diamonds API failed: {response.status_code}')
            print(f'   Error: {response.text}')
        
        # Test 2: Check vendors API
        print('\n2. Testing Vendors API...')
        response = requests.get(f'{BASE_URL}/vendors', headers=headers)
        if response.status_code == 200:
            vendors = response.json()
            print(f'   ✅ Vendors API working: {len(vendors)} vendors available')
            if vendors:
                print(f'   Sample vendor: ID {vendors[0]["id"]}, Name: {vendors[0]["name"]}')
        else:
            print(f'   ❌ Vendors API failed: {response.status_code}')
        
        # Test 3: Test simple manufacturing request creation
        print('\n3. Testing Simple Manufacturing Request Creation...')
        if diamonds and vendors:
            test_diamond = diamonds[0]
            test_vendor = vendors[0]
            
            # Use minimal required data
            manufacturing_data = {
                'vendor_id': test_vendor['id'],
                'sent_date': '2025-07-21',
                'expected_return_date': '2025-08-21',
                'diamonds': [{
                    'diamond_id': test_diamond['id'],
                    'quantity': 1
                }]
            }
            
            print(f'   Creating request with:')
            print(f'   • Vendor: {test_vendor["name"]} (ID: {test_vendor["id"]})')
            print(f'   • Diamond: {test_diamond["shape"]} {test_diamond["carat"]}ct (ID: {test_diamond["id"]})')
            print(f'   • Quantity: 1')
            
            response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
            print(f'   Response status: {response.status_code}')
            
            if response.status_code == 201:
                result = response.json()
                print(f'   ✅ Successfully created manufacturing request')
                print(f'   • Request ID: {result.get("id")}')
                print(f'   • Order Number: {result.get("order_number")}')
                print(f'   • Status: {result.get("status")}')
                
                # Check diamonds in response
                diamonds_in_response = result.get('diamonds', [])
                print(f'   • Diamonds in response: {len(diamonds_in_response)}')
                if diamonds_in_response:
                    diamond_data = diamonds_in_response[0]
                    print(f'   • Diamond details included: {diamond_data.get("diamond") is not None}')
                    if diamond_data.get("diamond"):
                        diamond_info = diamond_data["diamond"]
                        print(f'   • Diamond info: {diamond_info.get("shape")} {diamond_info.get("carat")}ct')
            else:
                print(f'   ❌ Failed to create manufacturing request')
                print(f'   Error: {response.text}')
        
        print('\n🎯 FRONTEND TESTING CHECKLIST:')
        print('   1. Open http://localhost:5173/manufacturing/new')
        print('   2. Check if diamonds dropdown shows available diamonds')
        print('   3. Select a diamond and verify quantity shows remaining available')
        print('   4. Try adding multiple diamonds')
        print('   5. Test quantity validation (try entering more than available)')
        print('   6. Check if Add Diamond button disables when no diamonds available')
        print('   7. Verify form validation summary shows correctly')
        print('   8. Test form submission')
        
    else:
        print(f'❌ Login failed: {response.status_code}')
        print(response.text)

if __name__ == '__main__':
    test_frontend_diamond_selection()
