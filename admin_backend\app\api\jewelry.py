from flask_restx import Namespace, Resource, fields, reqparse
from flask import request, current_app
from app.models.jewelry import JewelryItem, jewelry_diamonds
from app.models.diamond import Diamond
from app import db
from app.utils.decorators import token_required
from app.utils.error_handler import handle_errors
from werkzeug.utils import secure_filename
from datetime import datetime
import os

jewelry_ns = Namespace('jewelry', description='Jewelry inventory and management', path='/jewelry')

diamond_link_model = jewelry_ns.model('JewelryDiamond', {
    'diamond_id': fields.Integer(required=True),
    'quantity': fields.Integer(required=True)
})

jewelry_model = jewelry_ns.model('Jewelry', {
    'id': fields.Integer(readOnly=True),
    'name': fields.String(required=True),
    'design_code': fields.String(required=True),
    'vendor_id': fields.Integer(required=True),
    'gross_weight': fields.Float(required=True),
    'metal_type': fields.String(required=True),
    'received_date': fields.String,
    'status': fields.String,
    'image_path': fields.String,
    'diamonds': fields.List(fields.Nested(diamond_link_model))
})

create_jewelry_model = jewelry_ns.model('CreateJewelry', {
    'name': fields.String(required=True),
    'design_code': fields.String(required=True),
    'vendor_id': fields.Integer(required=True),
    'gross_weight': fields.Float(required=True),
    'metal_type': fields.String(required=True),
    'received_date': fields.String,
    'status': fields.String,
    'diamonds': fields.List(fields.Nested(diamond_link_model))
})

error_model = jewelry_ns.model('JewelryError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

def jewelry_to_dict(i):
    """Convert jewelry item to dictionary with safe field access and complete data."""
    try:
        # Get diamonds with association table data
        diamonds_data = []
        if hasattr(i, 'id') and i.id:
            from sqlalchemy import text
            query = text("""
                SELECT
                    jd.diamond_id,
                    jd.quantity,
                    jd.setting_type,
                    jd.position,
                    jd.notes,
                    d.shape_id,
                    s.name as shape_name,
                    d.carat,
                    d.color,
                    d.clarity,
                    d.cut_grade,
                    d.certificate_no,
                    d.cost_price,
                    d.retail_price,
                    d.status,
                    d.location
                FROM jewelry_diamonds jd
                LEFT JOIN diamonds d ON jd.diamond_id = d.id
                LEFT JOIN shapes s ON d.shape_id = s.id
                WHERE jd.jewelry_id = :jewelry_id
            """)

            result = db.session.execute(query, {'jewelry_id': i.id})
            for row in result:
                diamond_data = {
                    'diamond_id': row.diamond_id,
                    'quantity': row.quantity,
                    'setting_type': row.setting_type,
                    'position': row.position,
                    'notes': row.notes,
                    'diamond': {
                        'id': row.diamond_id,
                        'shape': row.shape_name,
                        'shape_id': row.shape_id,
                        'carat': row.carat,
                        'color': row.color,
                        'clarity': row.clarity,
                        'cut_grade': row.cut_grade,
                        'certificate_no': row.certificate_no,
                        'cost_price': row.cost_price,
                        'retail_price': row.retail_price,
                        'status': row.status,
                        'location': row.location
                    } if row.diamond_id else None
                }
                diamonds_data.append(diamond_data)

        return {
            'id': i.id,
            'name': i.name,
            'design_code': getattr(i, 'design_code', None),
            'vendor_id': i.vendor_id,
            'vendor': {
                'id': i.vendor.id,
                'name': i.vendor.name
            } if i.vendor else None,
            'category': getattr(i, 'category', None),
            'subcategory': getattr(i, 'subcategory', None),
            'gross_weight': getattr(i, 'gross_weight', None),
            'net_weight': getattr(i, 'net_weight', None),
            'metal_type': getattr(i, 'metal_type', None),
            'metal_purity': getattr(i, 'metal_purity', None),
            'cost_price': getattr(i, 'cost_price', None),
            'retail_price': getattr(i, 'retail_price', None),
            'making_charges': getattr(i, 'making_charges', None),
            'profit_margin': getattr(i, 'profit_margin', None),
            'status': getattr(i, 'status', None),
            'image_path': getattr(i, 'image_path', None),
            'received_date': i.received_date.isoformat() if getattr(i, 'received_date', None) else None,
            'created_at': i.created_at.isoformat() if getattr(i, 'created_at', None) else None,
            'updated_at': i.updated_at.isoformat() if getattr(i, 'updated_at', None) else None,
            'diamonds': diamonds_data
        }
    except Exception as e:
        # Fallback to basic fields only
        return {
            'id': getattr(i, 'id', None),
            'name': getattr(i, 'name', None),
            'vendor_id': getattr(i, 'vendor_id', None),
            'status': getattr(i, 'status', None),
            'error': f'Error serializing jewelry: {str(e)}'
        }

@jewelry_ns.route('/')
class JewelryList(Resource):
    @jewelry_ns.doc('list_jewelry')
    @jewelry_ns.response(200, 'List of jewelry items', [jewelry_model])
    @jewelry_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """List all jewelry items (optionally filter by status/vendor_id/design_code)"""
        query = JewelryItem.query
        for field in ['status', 'vendor_id', 'design_code']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(JewelryItem, field) == value)
        
        # Pagination
        page = request.args.get('page', default=1, type=int)
        limit = request.args.get('limit', default=20, type=int)
        pagination = query.paginate(page=page, per_page=limit, error_out=False)
        items = pagination.items
        return {
            'data': [jewelry_to_dict(i) for i in items],
            'total': pagination.total,
            'page': page,
            'limit': limit
        }, 200

    @jewelry_ns.doc('create_jewelry')
    @jewelry_ns.expect(create_jewelry_model)
    @jewelry_ns.marshal_with(jewelry_model, code=201)
    @jewelry_ns.response(400, 'Validation error', error_model)
    @jewelry_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def post(self):
        """Create a new jewelry item"""
        data = request.get_json()
        from sqlalchemy.exc import IntegrityError
        from app.models.vendor import Vendor
        try:
            # Unique name validation
            if JewelryItem.query.filter_by(name=data['name']).first():
                jewelry_ns.abort(409, f"Jewelry with name '{data['name']}' already exists.")
            # Vendor validation
            vendor = Vendor.query.get(data['vendor_id'])
            if not vendor:
                jewelry_ns.abort(400, f"Vendor with id {data['vendor_id']} does not exist.")
            # Quantity validation for diamonds
            for d in data.get('diamonds', []):
                if d['quantity'] <= 0:
                    jewelry_ns.abort(400, f"Diamond quantity must be greater than 0.")
            i = JewelryItem(
                name=data['name'],
                design_code=data['design_code'],
                vendor_id=data['vendor_id'],
                gross_weight=data['gross_weight'],
                metal_type=data['metal_type'],
                received_date=datetime.strptime(data['received_date'], '%Y-%m-%d').date() if 'received_date' in data else None,
                status=data.get('status', 'in_stock')
            )
            db.session.add(i)
            db.session.flush()  # Get i.id
            for d in data.get('diamonds', []):
                diamond = Diamond.query.get(d['diamond_id'])
                if not diamond:
                    db.session.rollback()
                    jewelry_ns.abort(404, f'Diamond {d["diamond_id"]} not found')
                db.session.execute(jewelry_diamonds.insert().values(
                    jewelry_id=i.id, diamond_id=diamond.id, quantity=d['quantity']
                ))
            db.session.commit()
            return jewelry_to_dict(i), 201
        except IntegrityError:
            db.session.rollback()
            jewelry_ns.abort(409, "Database integrity error occurred.")
        except Exception as e:
            db.session.rollback()
            jewelry_ns.abort(400, f'Failed to create jewelry item: {str(e)}')

@jewelry_ns.route('/<int:jewelry_id>')
@jewelry_ns.param('jewelry_id', 'The jewelry item identifier')
class JewelryResource(Resource):
    @jewelry_ns.doc('get_jewelry')
    @jewelry_ns.marshal_with(jewelry_model)
    @token_required
    def get(self, jewelry_id):
        """Get a jewelry item by ID"""
        try:
            i = JewelryItem.query.get_or_404(jewelry_id)
            return jewelry_to_dict(i)
        except Exception as e:
            jewelry_ns.abort(400, f'Error retrieving jewelry item: {str(e)}')

    @jewelry_ns.doc('update_jewelry')
    @jewelry_ns.expect(create_jewelry_model)
    @jewelry_ns.marshal_with(jewelry_model)
    @token_required
    def put(self, jewelry_id):
        """Update a jewelry item"""
        try:
            i = JewelryItem.query.get_or_404(jewelry_id)
            data = request.get_json()
            for field in ['name', 'design_code', 'vendor_id', 'gross_weight', 'metal_type', 'status']:
                if field in data:
                    setattr(i, field, data[field])
            if 'received_date' in data:
                i.received_date = datetime.strptime(data['received_date'], '%Y-%m-%d').date()
            db.session.commit()
            return jewelry_to_dict(i)
        except Exception as e:
            db.session.rollback()
            jewelry_ns.abort(400, f'Error updating jewelry item: {str(e)}')

    @jewelry_ns.doc('delete_jewelry')
    @token_required
    def delete(self, jewelry_id):
        """Delete a jewelry item"""
        try:
            i = JewelryItem.query.get_or_404(jewelry_id)
            # Check for associated diamonds
            associated_diamonds = db.session.execute(
                jewelry_diamonds.select().where(jewelry_diamonds.c.jewelry_id == jewelry_id)
            ).fetchall()
            if associated_diamonds:
                jewelry_ns.abort(400, 'Cannot delete jewelry item with associated diamonds.')

            db.session.delete(i)
            db.session.commit()
            return {'message': 'Jewelry item deleted'}, 200
        except Exception as e:
            db.session.rollback()
            jewelry_ns.abort(400, f'Failed to delete jewelry item: {str(e)}')

# File upload parser for Swagger
upload_parser = reqparse.RequestParser()
upload_parser.add_argument('image', location='files', type='FileStorage', required=True)

@jewelry_ns.route('/<int:jewelry_id>/image')
@jewelry_ns.param('jewelry_id', 'The jewelry item identifier')
class JewelryImageUpload(Resource):
    @jewelry_ns.doc('upload_jewelry_image')
    @jewelry_ns.expect(upload_parser)
    @token_required
    def post(self, jewelry_id):
        """Upload an image for a jewelry item"""
        i = JewelryItem.query.get_or_404(jewelry_id)
        if 'image' not in request.files:
            jewelry_ns.abort(400, 'No image uploaded')
        image = request.files['image']
        filename = secure_filename(image.filename)
        upload_folder = os.path.join(current_app.root_path, 'static', 'uploads')
        os.makedirs(upload_folder, exist_ok=True)
        path = os.path.join(upload_folder, filename)
        image.save(path)
        i.image_path = f'/static/uploads/{filename}'
        db.session.commit()
        return {'image_path': i.image_path}

@jewelry_ns.route('/<int:jewelry_id>/mark-sold')
@jewelry_ns.param('jewelry_id', 'The jewelry item identifier')
class JewelryMarkSold(Resource):
    @jewelry_ns.doc('mark_jewelry_sold')
    @jewelry_ns.marshal_with(jewelry_model)
    @token_required
    def patch(self, jewelry_id):
        """Mark a jewelry item as sold"""
        i = JewelryItem.query.get_or_404(jewelry_id)
        i.status = 'sold'
        db.session.commit()
        return jewelry_to_dict(i)

@jewelry_ns.route('/<int:jewelry_id>/deduct-diamonds')
@jewelry_ns.param('jewelry_id', 'The jewelry item identifier')
class JewelryDeductDiamonds(Resource):
    @jewelry_ns.doc('deduct_diamonds_for_jewelry')
    @token_required
    def patch(self, jewelry_id):
        """Deduct diamonds used in a jewelry item"""
        i = JewelryItem.query.get_or_404(jewelry_id)
        associated_diamonds = db.session.execute(
            jewelry_diamonds.select().where(jewelry_diamonds.c.jewelry_id == jewelry_id)
        ).fetchall()

        if not associated_diamonds:
            jewelry_ns.abort(400, 'No associated diamonds to deduct.')

        try:
            for diamond in associated_diamonds:
                d = Diamond.query.get(diamond.diamond_id)
                if not d:
                    jewelry_ns.abort(400, f'Diamond with ID {diamond.diamond_id} does not exist.')
                if d.quantity < diamond.quantity:
                    jewelry_ns.abort(400, f'Not enough stock for diamond {diamond.diamond_id}')
                d.quantity -= diamond.quantity
                db.session.add(d)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            jewelry_ns.abort(500, f'Failed to deduct diamonds: {str(e)}')

        return {'message': 'Diamonds deducted successfully'}, 200
