from app import db
from datetime import date, datetime
from sqlalchemy import func

# Enhanced association table for many-to-many diamond-manufacturing with detailed tracking
manufacturing_diamonds = db.Table(
    'manufacturing_diamonds',
    db.Column('manufacturing_id', db.Integer, db.<PERSON>ey('manufacturing_requests.id', name='fk_manufacturing_diamonds_manufacturing_id'), primary_key=True),
    db.<PERSON>umn('diamond_id', db.Integer, db.<PERSON>('diamonds.id', name='fk_manufacturing_diamonds_diamond_id'), primary_key=True),
    db.Column('quantity', db.Integer, nullable=False, default=1),
    db.Column('original_weight', db.Float),  # Original diamond weight
    db.Column('final_weight', db.Float),  # Final weight after manufacturing
    db.Column('loss_weight', db.Float),  # Weight loss during manufacturing
    db.Column('notes', db.Text),  # Notes for this specific diamond in the order
    extend_existing=True
)

class ManufacturingRequest(db.Model):
    """Enhanced manufacturing request model for professional jewelry production workflow."""
    __tablename__ = 'manufacturing_requests'

    # Basic Information
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True)  # Unique order number
    vendor_id = db.Column(db.Integer, db.ForeignKey('vendors.id', name='fk_manufacturing_vendor_id'), nullable=False)

    # Order Details
    order_type = db.Column(db.String(50))  # cutting, polishing, setting, repair, etc.
    priority = db.Column(db.String(20), default='normal')  # urgent, high, normal, low
    description = db.Column(db.Text)  # Detailed description of work required
    special_instructions = db.Column(db.Text)  # Special instructions for the vendor

    # Dates and Timeline
    sent_date = db.Column(db.Date, default=date.today)
    expected_return_date = db.Column(db.Date, nullable=True)
    actual_return_date = db.Column(db.Date)
    promised_delivery_date = db.Column(db.Date)  # Date promised to customer

    # Status Tracking
    status = db.Column(db.String(30), default='draft')  # draft, sent, in_progress, quality_check, completed, cancelled, returned
    progress_percentage = db.Column(db.Integer, default=0)  # Progress percentage (0-100)

    # Quality and Inspection
    quality_check_status = db.Column(db.String(20))  # passed, failed, pending
    quality_notes = db.Column(db.Text)  # Quality inspection notes
    inspector_name = db.Column(db.String(100))  # Who inspected the work
    inspection_date = db.Column(db.Date)

    # Financial Information
    estimated_cost = db.Column(db.Float)  # Estimated manufacturing cost
    actual_cost = db.Column(db.Float)  # Actual cost charged
    advance_paid = db.Column(db.Float, default=0.0)  # Advance payment made
    balance_amount = db.Column(db.Float, default=0.0)  # Balance amount
    payment_status = db.Column(db.String(20), default='pending')  # pending, partial, paid

    # Weight Tracking
    total_original_weight = db.Column(db.Float)  # Total original weight of diamonds
    total_final_weight = db.Column(db.Float)  # Total final weight after manufacturing
    total_loss_weight = db.Column(db.Float)  # Total weight loss
    loss_percentage = db.Column(db.Float)  # Loss percentage

    # Additional Information
    notes = db.Column(db.Text)  # General notes
    internal_notes = db.Column(db.Text)  # Internal notes (not shared with vendor)
    images_path = db.Column(db.String(500))  # Path to images/documents

    # Tracking and Audit
    created_by = db.Column(db.String(100))  # Who created the order
    updated_by = db.Column(db.String(100))  # Who last updated the order
    created_at = db.Column(db.DateTime, default=func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())

    # Relationships
    vendor = db.relationship('Vendor', backref='manufacturing_requests')
    diamonds = db.relationship('Diamond', secondary=manufacturing_diamonds, backref='manufacturing_requests')

    # Industry-specific constants
    VALID_STATUSES = [
        'draft', 'sent', 'in_progress', 'quality_check',
        'completed', 'cancelled', 'returned', 'on_hold'
    ]

    ORDER_TYPES = [
        'Diamond Cutting', 'Diamond Polishing', 'Jewelry Setting',
        'Jewelry Repair', 'Custom Design', 'Resizing', 'Cleaning', 'Other'
    ]

    PRIORITIES = ['urgent', 'high', 'normal', 'low']
    QUALITY_STATUSES = ['passed', 'failed', 'pending', 'needs_rework']
    PAYMENT_STATUSES = ['pending', 'partial', 'paid', 'overdue']

    def __init__(self, **kwargs):
        super(ManufacturingRequest, self).__init__(**kwargs)
        if not self.order_number:
            self.generate_order_number()

    def generate_order_number(self):
        """Generate a unique order number"""
        timestamp = datetime.now().strftime('%y%m%d%H%M')
        self.order_number = f"MFG{timestamp}"

    def calculate_loss_percentage(self):
        """Calculate weight loss percentage"""
        if self.total_original_weight and self.total_final_weight:
            loss = self.total_original_weight - self.total_final_weight
            self.total_loss_weight = loss
            self.loss_percentage = (loss / self.total_original_weight) * 100

    def update_progress(self, percentage):
        """Update progress percentage and status"""
        self.progress_percentage = max(0, min(100, percentage))

        # Auto-update status based on progress
        if percentage == 0:
            self.status = 'sent'
        elif percentage < 100:
            self.status = 'in_progress'
        else:
            self.status = 'quality_check'

    def is_overdue(self):
        """Check if the order is overdue"""
        if self.expected_return_date and self.status not in ['completed', 'cancelled']:
            return date.today() > self.expected_return_date
        return False

    def get_days_remaining(self):
        """Get days remaining until expected return date"""
        if self.expected_return_date and self.status not in ['completed', 'cancelled']:
            delta = self.expected_return_date - date.today()
            return delta.days
        return None

    def calculate_balance_amount(self):
        """Calculate balance amount"""
        if self.actual_cost:
            self.balance_amount = self.actual_cost - (self.advance_paid or 0)

    @classmethod
    def validate_status(cls, status):
        """Validate manufacturing status"""
        return status in cls.VALID_STATUSES

    @classmethod
    def validate_order_type(cls, order_type):
        """Validate order type"""
        return order_type in cls.ORDER_TYPES

    def to_dict(self):
        """Convert manufacturing request to dictionary for API responses"""
        # Get diamonds with association table data
        diamonds_data = []
        if self.id:
            # Query the association table to get the detailed diamond information
            from sqlalchemy import text
            query = text("""
                SELECT
                    md.diamond_id,
                    md.quantity,
                    md.original_weight,
                    md.final_weight,
                    md.loss_weight,
                    md.notes,
                    d.shape_id,
                    s.name as shape_name,
                    d.carat,
                    d.color,
                    d.clarity,
                    d.cut_grade,
                    d.certificate_no,
                    d.cost_price,
                    d.retail_price,
                    d.status,
                    d.location
                FROM manufacturing_diamonds md
                LEFT JOIN diamonds d ON md.diamond_id = d.id
                LEFT JOIN shapes s ON d.shape_id = s.id
                WHERE md.manufacturing_id = :manufacturing_id
            """)

            result = db.session.execute(query, {'manufacturing_id': self.id})
            for row in result:
                diamond_data = {
                    'diamond_id': row.diamond_id,
                    'quantity': row.quantity,
                    'original_weight': row.original_weight,
                    'final_weight': row.final_weight,
                    'loss_weight': row.loss_weight,
                    'notes': row.notes,
                    'diamond': {
                        'id': row.diamond_id,
                        'shape': row.shape_name,
                        'shape_id': row.shape_id,
                        'carat': row.carat,
                        'color': row.color,
                        'clarity': row.clarity,
                        'cut_grade': row.cut_grade,
                        'certificate_no': row.certificate_no,
                        'cost_price': row.cost_price,
                        'retail_price': row.retail_price,
                        'status': row.status,
                        'location': row.location
                    } if row.diamond_id else None
                }
                diamonds_data.append(diamond_data)

        return {
            'id': self.id,
            'order_number': self.order_number,
            'vendor_id': self.vendor_id,
            'vendor': self.vendor.to_dict() if self.vendor else None,
            'order_type': self.order_type,
            'priority': self.priority,
            'description': self.description,
            'special_instructions': self.special_instructions,
            'sent_date': self.sent_date.isoformat() if self.sent_date else None,
            'expected_return_date': self.expected_return_date.isoformat() if self.expected_return_date else None,
            'actual_return_date': self.actual_return_date.isoformat() if self.actual_return_date else None,
            'promised_delivery_date': self.promised_delivery_date.isoformat() if self.promised_delivery_date else None,
            'status': self.status,
            'progress_percentage': self.progress_percentage,
            'quality_check_status': self.quality_check_status,
            'quality_notes': self.quality_notes,
            'inspector_name': self.inspector_name,
            'inspection_date': self.inspection_date.isoformat() if self.inspection_date else None,
            'estimated_cost': self.estimated_cost,
            'actual_cost': self.actual_cost,
            'advance_paid': self.advance_paid,
            'balance_amount': self.balance_amount,
            'payment_status': self.payment_status,
            'total_original_weight': self.total_original_weight,
            'total_final_weight': self.total_final_weight,
            'total_loss_weight': self.total_loss_weight,
            'loss_percentage': self.loss_percentage,
            'notes': self.notes,
            'internal_notes': self.internal_notes,
            'images_path': self.images_path,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'diamonds': diamonds_data,
            'is_overdue': self.is_overdue(),
            'days_remaining': self.get_days_remaining()
        }

    def __repr__(self):
        return f'<ManufacturingRequest {self.order_number} - Vendor {self.vendor_id}>'
