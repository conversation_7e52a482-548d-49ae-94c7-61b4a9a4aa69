#!/usr/bin/env python3

import requests
import json

def debug_manufacturing_api():
    """Debug manufacturing API issues"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        print('🔍 DEBUGGING MANUFACTURING API ISSUES')
        print('=' * 50)
        
        # Test 1: Manufacturing list API
        print('\n1. Testing Manufacturing List API...')
        response = requests.get(f'{BASE_URL}/manufacturing', headers=headers)
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'   Response type: {type(data)}')
            print(f'   Response length: {len(data) if isinstance(data, list) else "Not a list"}')
            if isinstance(data, list) and len(data) > 0:
                print(f'   First item keys: {list(data[0].keys())}')
                print(f'   Sample item: {json.dumps(data[0], indent=2)[:500]}...')
            else:
                print(f'   Response: {data}')
        else:
            print(f'   Error: {response.text}')
        
        # Test 2: Diamonds API
        print('\n2. Testing Diamonds API...')
        response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'   Response type: {type(data)}')
            if isinstance(data, dict):
                print(f'   Response keys: {list(data.keys())}')
                diamonds = data.get('data', [])
                print(f'   Diamonds count: {len(diamonds)}')
                if diamonds:
                    print(f'   First diamond: ID {diamonds[0].get("id")}, {diamonds[0].get("shape")}, Qty: {diamonds[0].get("quantity")}')
            else:
                print(f'   Response: {data}')
        else:
            print(f'   Error: {response.text}')
            
        # Test 3: Dashboard API
        print('\n3. Testing Dashboard API...')
        response = requests.get(f'{BASE_URL}/dashboard/summary', headers=headers)
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'   Manufacturing count in dashboard: {data.get("manufacturing", {}).get("open", "Not found")}')
            print(f'   Dashboard response: {json.dumps(data, indent=2)}')
        else:
            print(f'   Error: {response.text}')
            
        # Test 4: Vendors API
        print('\n4. Testing Vendors API...')
        response = requests.get(f'{BASE_URL}/vendors', headers=headers)
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'   Vendors count: {len(data) if isinstance(data, list) else "Not a list"}')
            if isinstance(data, list) and len(data) > 0:
                print(f'   First vendor: {data[0]}')
        else:
            print(f'   Error: {response.text}')
            
    else:
        print(f'Login failed: {response.status_code}')
        print(response.text)

if __name__ == '__main__':
    debug_manufacturing_api()
