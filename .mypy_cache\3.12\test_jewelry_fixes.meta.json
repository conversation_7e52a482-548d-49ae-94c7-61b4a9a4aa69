{"data_mtime": 1753261531, "dep_lines": [98, 3, 4, 5, 98, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["PIL.Image", "requests", "json", "os", "PIL", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "http", "http.cookiejar", "io", "requests.auth", "requests.models", "types", "typing"], "hash": "c0059cb0f94e31a39d74c255030d845408ee12f9", "id": "test_jewelry_fixes", "ignore_all": false, "interface_hash": "044a32215e1828524511a7ac5971fd463e8f84ba", "mtime": 1753261527, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\test_jewelry_fixes.py", "plugin_data": null, "size": 9020, "suppressed": [], "version_id": "1.15.0"}