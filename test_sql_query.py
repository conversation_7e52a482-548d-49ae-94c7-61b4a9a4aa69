#!/usr/bin/env python3

import sys
import os
sys.path.append('admin_backend')

from app import create_app, db
from sqlalchemy import text

def test_sql_query():
    """Test the SQL query for manufacturing diamonds"""
    
    app = create_app()
    with app.app_context():
        # Test the query
        query = text("""
            SELECT 
                md.manufacturing_id,
                md.diamond_id,
                md.quantity,
                md.original_weight,
                md.final_weight,
                md.loss_weight,
                md.notes,
                d.shape_id,
                s.name as shape_name,
                d.carat,
                d.color,
                d.clarity,
                d.cut_grade,
                d.certificate_no,
                d.cost_price,
                d.retail_price,
                d.status,
                d.location
            FROM manufacturing_diamonds md
            LEFT JOIN diamonds d ON md.diamond_id = d.id
            LEFT JOIN shapes s ON d.shape_id = s.id
            ORDER BY md.manufacturing_id
        """)
        
        result = db.session.execute(query)
        print("Manufacturing diamonds data:")
        for row in result:
            print(f"Manufacturing ID: {row.manufacturing_id}, Diamond ID: {row.diamond_id}, Quantity: {row.quantity}")
            if row.diamond_id:
                print(f"  Diamond: {row.shape_name} {row.carat}ct {row.color} {row.clarity}")
            print()

if __name__ == '__main__':
    test_sql_query()
