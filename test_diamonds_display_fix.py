#!/usr/bin/env python3

import requests
import json

def test_manufacturing_diamonds_display():
    """Test that manufacturing diamonds are now showing properly"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        print("🔧 Testing Manufacturing Diamonds Display Fix")
        print("=" * 50)
        
        # Get manufacturing requests
        response = requests.get(f'{BASE_URL}/manufacturing', headers=headers)
        if response.status_code == 200:
            requests_data = response.json()
            print(f"✅ Found {len(requests_data)} manufacturing requests")
            
            for i, request in enumerate(requests_data):
                print(f"\n📋 Manufacturing Request {i+1}:")
                print(f"   ID: {request['id']}")
                print(f"   Order Number: {request['order_number']}")
                print(f"   Status: {request['status']}")
                print(f"   Vendor: {request.get('vendor', {}).get('name', 'Unknown')}")
                
                diamonds = request.get('diamonds', [])
                print(f"   Diamonds: {len(diamonds)}")
                
                for j, diamond in enumerate(diamonds):
                    print(f"   💎 Diamond {j+1}:")
                    print(f"      Diamond ID: {diamond.get('diamond_id')}")
                    print(f"      Quantity: {diamond.get('quantity')}")
                    
                    # Check if diamond details are included
                    diamond_details = diamond.get('diamond')
                    if diamond_details:
                        print(f"      ✅ Diamond Details Found:")
                        print(f"         Shape: {diamond_details.get('shape')}")
                        print(f"         Carat: {diamond_details.get('carat')}")
                        print(f"         Color: {diamond_details.get('color')}")
                        print(f"         Clarity: {diamond_details.get('clarity')}")
                        print(f"         Certificate: {diamond_details.get('certificate_no')}")
                    else:
                        print(f"      ❌ Diamond Details Missing")
                        
            # Test summary
            print(f"\n🎯 Test Summary:")
            total_diamonds = sum(len(req.get('diamonds', [])) for req in requests_data)
            diamonds_with_details = sum(
                1 for req in requests_data 
                for diamond in req.get('diamonds', []) 
                if diamond.get('diamond') is not None
            )
            
            print(f"   Total diamonds in manufacturing: {total_diamonds}")
            print(f"   Diamonds with details: {diamonds_with_details}")
            
            if diamonds_with_details == total_diamonds and total_diamonds > 0:
                print("   ✅ ALL DIAMONDS SHOWING PROPERLY!")
            elif diamonds_with_details > 0:
                print("   ⚠️  SOME DIAMONDS SHOWING PROPERLY")
            else:
                print("   ❌ NO DIAMONDS SHOWING DETAILS")
                print("   🔄 Backend server needs to be restarted to pick up the fix")
                
        else:
            print(f'❌ Failed to get manufacturing requests: {response.status_code}')
            print(response.text)
    else:
        print(f'❌ Login failed: {response.status_code}')
        print(response.text)

if __name__ == '__main__':
    test_manufacturing_diamonds_display()
