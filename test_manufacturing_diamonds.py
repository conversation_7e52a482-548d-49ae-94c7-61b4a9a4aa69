#!/usr/bin/env python3

import requests
import json

def test_manufacturing_diamonds():
    """Test manufacturing diamonds display issue"""
    
    # Test the manufacturing API to see diamond structure
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # Get available diamonds first
        response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
        if response.status_code == 200:
            diamonds_data = response.json()
            diamonds = diamonds_data.get('data', [])
            available_diamonds = [d for d in diamonds if d.get('quantity', 0) > 0]
            print(f'Available diamonds: {len(available_diamonds)}')
            
            if available_diamonds:
                test_diamond = available_diamonds[0]
                print(f'Test diamond: ID {test_diamond["id"]}, Shape: {test_diamond.get("shape")}, Qty: {test_diamond["quantity"]}')
                
                # Get vendors
                response = requests.get(f'{BASE_URL}/vendors', headers=headers)
                if response.status_code == 200:
                    vendors = response.json()
                    if vendors:
                        vendor_id = vendors[0]['id']
                        print(f'Using vendor ID: {vendor_id}')
                        
                        # Create a new manufacturing request with a diamond
                        manufacturing_data = {
                            'vendor_id': vendor_id,
                            'sent_date': '2025-07-21',
                            'expected_return_date': '2025-08-21',
                            'diamonds': [{
                                'diamond_id': test_diamond['id'],
                                'quantity': 1
                            }]
                        }
                        
                        response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
                        if response.status_code == 201:
                            new_request = response.json()
                            print(f'Created manufacturing request: {new_request["id"]}')
                            print('Diamonds in new request:')
                            diamonds = new_request.get('diamonds', [])
                            for i, diamond in enumerate(diamonds):
                                print(f'Diamond {i+1}: {json.dumps(diamond, indent=2)}')
                        else:
                            print(f'Failed to create manufacturing request: {response.status_code}')
                            print(response.text)
            else:
                print('No available diamonds found')
        else:
            print(f'Failed to get diamonds: {response.status_code}')
            print(response.text)
    else:
        print(f'Login failed: {response.status_code}')
        print(response.text)

if __name__ == '__main__':
    test_manufacturing_diamonds()
