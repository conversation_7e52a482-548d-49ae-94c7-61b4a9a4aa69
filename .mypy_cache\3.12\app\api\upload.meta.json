{"data_mtime": 1753099813, "dep_lines": [3, 4, 2, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30, 30, 5], "dependencies": ["app.utils.decorators", "werkzeug.utils", "flask", "os", "builtins", "_frozen_importlib", "abc", "app.utils", "typing"], "hash": "d7a3056b04b35aee7c3ab4a1970cf955fd22381f", "id": "app.api.upload", "ignore_all": true, "interface_hash": "9aadf0505426e11821838170e9eed1bf4e69f953", "mtime": 1750528772, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\upload.py", "plugin_data": null, "size": 1884, "suppressed": ["flask_restx"], "version_id": "1.15.0"}