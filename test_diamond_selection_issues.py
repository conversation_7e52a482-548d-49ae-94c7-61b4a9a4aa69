#!/usr/bin/env python3

import requests
import json

def test_diamond_selection_issues():
    """Test diamond selection and quantity management issues"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        print('🔍 TESTING DIAMOND SELECTION ISSUES')
        print('=' * 50)
        
        # Test 1: Get available diamonds
        print('\n1. Testing Available Diamonds...')
        response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
        if response.status_code == 200:
            data = response.json()
            diamonds = data.get('data', [])
            available_diamonds = [d for d in diamonds if d.get('quantity', 0) > 0]
            print(f'   ✅ Available diamonds: {len(available_diamonds)}')
            
            print('\n   Diamond Inventory:')
            for diamond in available_diamonds:
                print(f'      ID {diamond["id"]}: {diamond["shape"]} {diamond["carat"]}ct - Qty: {diamond["quantity"]}')
                
            # Test scenarios with different quantities
            if len(available_diamonds) >= 2:
                diamond1 = available_diamonds[0]
                diamond2 = available_diamonds[1]
                
                print(f'\n2. Testing Manufacturing Request Creation Scenarios...')
                
                # Scenario 1: Single diamond, partial quantity
                print(f'\n   Scenario 1: Single diamond, partial quantity')
                print(f'   Using Diamond ID {diamond1["id"]} (Available: {diamond1["quantity"]})')
                
                test_quantity = min(diamond1["quantity"] - 1, 1) if diamond1["quantity"] > 1 else 1
                manufacturing_data = {
                    'vendor_id': 1,
                    'sent_date': '2025-07-21',
                    'expected_return_date': '2025-08-21',
                    'order_type': 'Diamond Cutting',
                    'diamonds': [{
                        'diamond_id': diamond1['id'],
                        'quantity': test_quantity
                    }]
                }
                
                response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
                if response.status_code == 201:
                    print(f'   ✅ Created manufacturing request with {test_quantity} diamonds')
                    
                    # Check updated diamond quantity
                    response = requests.get(f'{BASE_URL}/diamonds/{diamond1["id"]}', headers=headers)
                    if response.status_code == 200:
                        updated_diamond = response.json()
                        expected_qty = diamond1["quantity"] - test_quantity
                        actual_qty = updated_diamond.get('quantity', 0)
                        print(f'   Expected remaining quantity: {expected_qty}')
                        print(f'   Actual remaining quantity: {actual_qty}')
                        if actual_qty == expected_qty:
                            print('   ✅ Quantity updated correctly')
                        else:
                            print('   ❌ Quantity update incorrect')
                else:
                    print(f'   ❌ Failed to create: {response.status_code}')
                    print(f'       Error: {response.text}')
                
                # Scenario 2: Multiple diamonds, different quantities
                print(f'\n   Scenario 2: Multiple diamonds, different quantities')
                print(f'   Using Diamond ID {diamond1["id"]} and {diamond2["id"]}')
                
                # Get fresh diamond data
                response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
                if response.status_code == 200:
                    fresh_data = response.json()
                    fresh_diamonds = fresh_data.get('data', [])
                    fresh_diamond1 = next((d for d in fresh_diamonds if d['id'] == diamond1['id']), None)
                    fresh_diamond2 = next((d for d in fresh_diamonds if d['id'] == diamond2['id']), None)
                    
                    if fresh_diamond1 and fresh_diamond2 and fresh_diamond1['quantity'] > 0 and fresh_diamond2['quantity'] > 0:
                        manufacturing_data = {
                            'vendor_id': 1,
                            'sent_date': '2025-07-21',
                            'expected_return_date': '2025-08-21',
                            'order_type': 'Diamond Polishing',
                            'diamonds': [
                                {
                                    'diamond_id': fresh_diamond1['id'],
                                    'quantity': min(fresh_diamond1['quantity'], 1)
                                },
                                {
                                    'diamond_id': fresh_diamond2['id'],
                                    'quantity': min(fresh_diamond2['quantity'], 2)
                                }
                            ]
                        }
                        
                        response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
                        if response.status_code == 201:
                            print(f'   ✅ Created multi-diamond manufacturing request')
                        else:
                            print(f'   ❌ Failed to create multi-diamond request: {response.status_code}')
                            print(f'       Error: {response.text}')
                    else:
                        print('   ⚠️  Insufficient diamond quantities for multi-diamond test')
                
                # Scenario 3: Test quantity validation (should fail)
                print(f'\n   Scenario 3: Testing quantity validation (over-allocation)')
                
                # Get fresh diamond data again
                response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
                if response.status_code == 200:
                    fresh_data = response.json()
                    fresh_diamonds = fresh_data.get('data', [])
                    test_diamond = next((d for d in fresh_diamonds if d['quantity'] > 0), None)
                    
                    if test_diamond:
                        over_quantity = test_diamond['quantity'] + 10
                        manufacturing_data = {
                            'vendor_id': 1,
                            'sent_date': '2025-07-21',
                            'expected_return_date': '2025-08-21',
                            'order_type': 'Diamond Cutting',
                            'diamonds': [{
                                'diamond_id': test_diamond['id'],
                                'quantity': over_quantity
                            }]
                        }
                        
                        response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
                        if response.status_code == 400:
                            print(f'   ✅ Correctly rejected over-allocation (requested {over_quantity}, available {test_diamond["quantity"]})')
                        else:
                            print(f'   ❌ Should have rejected over-allocation but got: {response.status_code}')
                
            else:
                print('   ⚠️  Need at least 2 diamonds for comprehensive testing')
                
        else:
            print(f'   ❌ Failed to get diamonds: {response.status_code}')
            
        print('\n🎯 ISSUES TO CHECK IN FRONTEND:')
        print('   1. Diamond options should update when quantities change')
        print('   2. Available quantity should reflect selected quantities')
        print('   3. Add Diamond should work correctly')
        print('   4. Quantity validation should prevent over-allocation')
        print('   5. Diamond should be removed from options when fully allocated')
        
    else:
        print(f'❌ Login failed: {response.status_code}')

if __name__ == '__main__':
    test_diamond_selection_issues()
