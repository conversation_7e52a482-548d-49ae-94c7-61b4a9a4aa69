import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Edit, Trash2, Upload, Eye, ShoppingCart } from 'lucide-react';
import { api } from '../../lib/api';
import { Jewelry } from '../../types';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Modal from '../../components/ui/Modal';
import { LoadingState, ErrorState, EmptyState } from '../../components/DataStates';
import JewelryForm from './JewelryForm';
import JewelryDetails from './JewelryDetails';
import ImageUpload from './ImageUpload';
import toast from 'react-hot-toast';

const JewelryList: React.FC = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [selectedJewelry, setSelectedJewelry] = useState<Jewelry | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [vendorFilter, setVendorFilter] = useState('');

  const queryClient = useQueryClient();

  const { data: jewelry, isLoading, error, refetch } = useQuery({
    queryKey: ['jewelry', { search: searchTerm, status: statusFilter, vendor: vendorFilter }],
    queryFn: async (): Promise<Jewelry[]> => {
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (statusFilter) params.status = statusFilter;
      if (vendorFilter) params.vendor_id = vendorFilter;

      const response = await api.jewelry.list(params);
      // Handle both direct array and paginated response formats
      if (Array.isArray(response)) {
        return response;
      } else if (response?.data && Array.isArray(response.data)) {
        return response.data;
      }
      return [];
    },
    retry: 1
  });

  const { data: vendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await api.vendors.list();
      // The API service already returns response.data, so we don't need to access .data again
      return Array.isArray(response) ? response : response?.data || [];
    }
  });

  // Enhanced error handling for API calls
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await api.delete(`/jewelry/${id}/`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jewelry'] });
      toast.success('Jewelry item deleted successfully');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to delete jewelry item';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  const markSoldMutation = useMutation({
    mutationFn: async (id: number) => {
      await api.patch(`/jewelry/${id}/mark-sold/`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jewelry'] });
      toast.success('Jewelry item marked as sold');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to mark jewelry as sold';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  const handleEdit = (item: Jewelry) => {
    setSelectedJewelry(item);
    setIsEditModalOpen(true);
  };

  const handleViewDetails = (item: Jewelry) => {
    setSelectedJewelry(item);
    setIsDetailsModalOpen(true);
  };

  const handleUploadImage = (item: Jewelry) => {
    setSelectedJewelry(item);
    setIsImageModalOpen(true);
  };

  const handleMarkSold = async (item: Jewelry) => {
    if (window.confirm('Are you sure you want to mark this jewelry item as sold?')) {
      markSoldMutation.mutate(item.id);
    }
  };

  const handleDelete = async (item: Jewelry) => {
    if (window.confirm('Are you sure you want to delete this jewelry item?')) {
      deleteMutation.mutate(item.id);
    }
  };

  const statusOptions = [
    { value: 'in_stock', label: 'In Stock' },
    { value: 'sold', label: 'Sold' }
  ];

  const vendorOptions = vendors?.map((vendor: any) => ({
    value: vendor.id,
    label: vendor.name
  })) || [];

  if (isLoading) {
    return <LoadingState message="Loading jewelry inventory..." />;
  }

  if (error) {
    return (
      <ErrorState 
        title="Failed to load jewelry inventory"
        onRetry={refetch}
      />
    );
  }

  if (!jewelry || jewelry.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Jewelry Inventory</h1>
            <p className="text-gray-600">Manage your finished jewelry items</p>
          </div>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Jewelry
          </Button>
        </div>
        
        <EmptyState
          title="No jewelry items found"
          description="Start by adding your first jewelry item to the inventory"
          action={{
            label: "Add Jewelry",
            onClick: () => setIsAddModalOpen(true)
          }}
        />
        
        {/* Modals */}
        <Modal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          title="Add New Jewelry"
        >
          <JewelryForm onSuccess={() => setIsAddModalOpen(false)} />
        </Modal>
      </div>
    );
  }

  // Refactored card rendering logic
  const renderJewelryCards = (jewelry: Jewelry[]) => {
    if (!Array.isArray(jewelry)) {
      return <div className="text-center text-gray-500">No jewelry items available</div>;
    }
    return jewelry.map((item) => (
      <Card key={item.id} className="overflow-hidden">
        <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg mb-4">
          {item.image_path ? (
            <img
              src={`${process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000'}${item.image_path}`}
              alt={item.name}
              className="w-full h-48 object-cover rounded-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <div className={`w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center ${item.image_path ? 'hidden' : ''}`}>
            <span className="text-gray-400">No Image</span>
          </div>
        </div>

        <div className="space-y-3">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
            <p className="text-sm text-gray-500">{item.design_code}</p>
          </div>

          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-gray-500">Weight:</span>
              <span className="ml-1 font-medium">{item.gross_weight}g</span>
            </div>
            <div>
              <span className="text-gray-500">Metal:</span>
              <span className="ml-1 font-medium">{item.metal_type}</span>
            </div>
            <div>
              <span className="text-gray-500">Vendor:</span>
              <span className="ml-1 font-medium">{item.vendor?.name}</span>
            </div>
            <div>
              <span className="text-gray-500">Status:</span>
              <span className={`ml-1 font-medium ${
                item.status === 'in_stock' ? 'text-green-600' : 'text-red-600'
              }`}>
                {item.status === 'in_stock' ? 'In Stock' : 'Sold'}
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between pt-3 border-t border-gray-200">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleViewDetails(item)}
              >
                <Eye className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleEdit(item)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleUploadImage(item)}
              >
                <Upload className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex items-center space-x-2">
              {item.status === 'in_stock' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleMarkSold(item)}
                  isLoading={markSoldMutation.isPending}
                >
                  <ShoppingCart className="h-4 w-4 text-green-600" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDelete(item)}
              >
                <Trash2 className="h-4 w-4 text-red-600" />
              </Button>
            </div>
          </div>
        </div>
      </Card>
    ));
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Jewelry Inventory</h1>
          <p className="text-gray-600">Manage your finished jewelry items</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Jewelry
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Input
            placeholder="Search jewelry..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
          <Select
            options={statusOptions}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            placeholder="Filter by status"
          />
          <Select
            options={vendorOptions}
            value={vendorFilter}
            onChange={(e) => setVendorFilter(e.target.value)}
            placeholder="Filter by vendor"
          />
          <Button
            variant="ghost"
            onClick={() => {
              setSearchTerm('');
              setStatusFilter('');
              setVendorFilter('');
            }}
          >
            Clear Filters
          </Button>
        </div>
      </Card>

      {/* Jewelry Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {renderJewelryCards(Array.isArray(jewelry) ? jewelry : [])}
      </div>

      {jewelry?.length === 0 && (
        <Card>
          <div className="text-center py-12">
            <p className="text-gray-500">No jewelry items found matching your criteria.</p>
          </div>
        </Card>
      )}

      {/* Modals */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New Jewelry"
        size="xl"
      >
        <JewelryForm
          onSuccess={() => {
            setIsAddModalOpen(false);
            queryClient.invalidateQueries({ queryKey: ['jewelry'] });
          }}
        />
      </Modal>

      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedJewelry(null);
        }}
        title="Edit Jewelry"
        size="xl"
      >
        {selectedJewelry && (
          <JewelryForm
            jewelry={selectedJewelry}
            onSuccess={() => {
              setIsEditModalOpen(false);
              setSelectedJewelry(null);
              queryClient.invalidateQueries({ queryKey: ['jewelry'] });
            }}
          />
        )}
      </Modal>

      <Modal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedJewelry(null);
        }}
        title="Jewelry Details"
        size="lg"
      >
        {selectedJewelry && (
          <JewelryDetails jewelry={selectedJewelry} />
        )}
      </Modal>

      <Modal
        isOpen={isImageModalOpen}
        onClose={() => {
          setIsImageModalOpen(false);
          setSelectedJewelry(null);
        }}
        title="Upload Image"
        size="md"
      >
        {selectedJewelry && (
          <ImageUpload
            jewelryId={selectedJewelry.id}
            onSuccess={() => {
              setIsImageModalOpen(false);
              setSelectedJewelry(null);
              queryClient.invalidateQueries({ queryKey: ['jewelry'] });
            }}
          />
        )}
      </Modal>
    </div>
  );
};

export default JewelryList;