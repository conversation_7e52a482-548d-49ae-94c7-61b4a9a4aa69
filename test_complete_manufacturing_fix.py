#!/usr/bin/env python3

import requests
import json

def test_complete_manufacturing_fix():
    """Test all manufacturing fixes after backend restart"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        print('🔧 COMPLETE MANUFACTURING SYSTEM TEST')
        print('=' * 50)
        
        # Test 1: Manufacturing List API (should show diamond details)
        print('\n1. Testing Manufacturing List with Diamond Details...')
        response = requests.get(f'{BASE_URL}/manufacturing', headers=headers)
        if response.status_code == 200:
            requests_data = response.json()
            print(f'   ✅ Found {len(requests_data)} manufacturing requests')
            
            diamonds_with_details = 0
            total_diamonds = 0
            
            for i, request in enumerate(requests_data):
                diamonds = request.get('diamonds', [])
                total_diamonds += len(diamonds)
                
                for diamond in diamonds:
                    if diamond.get('diamond') is not None:
                        diamonds_with_details += 1
                        print(f'   ✅ Request {request["id"]}: Diamond {diamond["diamond"]["shape"]} {diamond["diamond"]["carat"]}ct')
                    else:
                        print(f'   ❌ Request {request["id"]}: Diamond details missing')
            
            print(f'\n   Summary: {diamonds_with_details}/{total_diamonds} diamonds have details')
            
        else:
            print(f'   ❌ Failed: {response.status_code}')
        
        # Test 2: Diamonds API for Manufacturing Form
        print('\n2. Testing Diamonds API for Form...')
        response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
        if response.status_code == 200:
            data = response.json()
            diamonds = data.get('data', [])
            available_diamonds = [d for d in diamonds if d.get('quantity', 0) > 0]
            print(f'   ✅ Available diamonds for manufacturing: {len(available_diamonds)}')
            
            if available_diamonds:
                for i, diamond in enumerate(available_diamonds[:3]):  # Show first 3
                    print(f'      {i+1}. ID {diamond["id"]}: {diamond["shape"]} {diamond["carat"]}ct (Qty: {diamond["quantity"]})')
        else:
            print(f'   ❌ Failed: {response.status_code}')
        
        # Test 3: Vendors API
        print('\n3. Testing Vendors API...')
        response = requests.get(f'{BASE_URL}/vendors', headers=headers)
        if response.status_code == 200:
            vendors = response.json()
            print(f'   ✅ Available vendors: {len(vendors)}')
            if vendors:
                for i, vendor in enumerate(vendors[:3]):  # Show first 3
                    print(f'      {i+1}. ID {vendor["id"]}: {vendor["name"]}')
        else:
            print(f'   ❌ Failed: {response.status_code}')
        
        # Test 4: Create New Manufacturing Request
        print('\n4. Testing Manufacturing Request Creation...')
        if available_diamonds and vendors:
            test_diamond = available_diamonds[0]
            test_vendor = vendors[0]
            
            manufacturing_data = {
                'vendor_id': test_vendor['id'],
                'sent_date': '2025-07-21',
                'expected_return_date': '2025-08-21',
                'order_type': 'Diamond Cutting',
                'priority': 'normal',
                'description': 'Test manufacturing request with diamond details',
                'diamonds': [{
                    'diamond_id': test_diamond['id'],
                    'quantity': 1
                }]
            }
            
            response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
            if response.status_code == 201:
                new_request = response.json()
                print(f'   ✅ Created manufacturing request: {new_request["id"]}')
                
                # Check if the new request has diamond details
                diamonds = new_request.get('diamonds', [])
                if diamonds and diamonds[0].get('diamond'):
                    diamond_info = diamonds[0]['diamond']
                    print(f'   ✅ Diamond details included: {diamond_info["shape"]} {diamond_info["carat"]}ct')
                else:
                    print(f'   ❌ Diamond details missing in new request')
            else:
                print(f'   ❌ Failed to create: {response.status_code}')
                print(f'       Error: {response.text}')
        
        print('\n🎯 FINAL ASSESSMENT:')
        print('   Frontend fixes applied:')
        print('   ✅ Manufacturing List: Fixed data parsing')
        print('   ✅ Manufacturing History: Fixed data parsing') 
        print('   ✅ Manufacturing Form: Added debugging for diamonds')
        print('   ')
        print('   Backend fixes applied:')
        print('   ✅ ManufacturingRequest.to_dict(): Enhanced with diamond details')
        print('   ')
        print('   🔄 RESTART BACKEND SERVER to see diamond details!')
        
    else:
        print(f'❌ Login failed: {response.status_code}')
        print(response.text)

if __name__ == '__main__':
    test_complete_manufacturing_fix()
