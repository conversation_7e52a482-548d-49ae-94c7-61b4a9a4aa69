{".class": "MypyFile", "_fullname": "test_vendor_api_fix", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BASE_URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "test_vendor_api_fix.BASE_URL", "name": "BASE_URL", "type": "builtins.str"}}, "LOGIN_URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_vendor_api_fix.LOGIN_URL", "name": "LOGIN_URL", "type": "builtins.str"}}, "TEST_EMAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "test_vendor_api_fix.TEST_EMAIL", "name": "TEST_EMAIL", "type": "builtins.str"}}, "TEST_PASSWORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "test_vendor_api_fix.TEST_PASSWORD", "name": "TEST_PASSWORD", "type": "builtins.str"}}, "VENDORS_URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_vendor_api_fix.VENDORS_URL", "name": "VENDORS_URL", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_vendor_api_fix.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_vendor_api_fix.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_vendor_api_fix.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_vendor_api_fix.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_vendor_api_fix.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_vendor_api_fix.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "success": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_vendor_api_fix.success", "name": "success", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "test_vendor_api_fix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_vendor_api_fix.test_vendor_api_fix", "name": "test_vendor_api_fix", "type": null}}}, "path": "E:\\admin_panel\\test_vendor_api_fix.py"}