import React from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Plus, Trash2 } from 'lucide-react';
import { api } from '../../lib/api';
import { ManufacturingRequest } from '../../types';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Button from '../../components/ui/Button';
import toast from 'react-hot-toast';
import {
  MANUFACTURING_STATUSES,
  ORDER_TYPES,
  PRIORITIES,
  QUALITY_STATUSES,
  PAYMENT_STATUSES,
  validateOrderData,
  VALIDATION_RANGES
} from '../../constants/manufacturing';

interface ManufacturingFormProps {
  manufacturing?: ManufacturingRequest;
  onSuccess: () => void;
}

interface ManufacturingFormData {
  // Basic Information
  vendor_id: string;
  order_type: string;
  priority?: string;
  description?: string;
  special_instructions?: string;

  // Dates and Timeline
  sent_date: string;
  expected_return_date: string;
  actual_return_date?: string;
  promised_delivery_date?: string;

  // Status Tracking
  status?: string;
  progress_percentage?: number;

  // Quality and Inspection
  quality_check_status?: string;
  quality_notes?: string;
  inspector_name?: string;
  inspection_date?: string;

  // Financial Information
  estimated_cost?: number;
  actual_cost?: number;
  advance_paid?: number;
  payment_status?: string;

  // Weight Tracking
  total_original_weight?: number;
  total_final_weight?: number;

  // Additional Information
  notes?: string;
  internal_notes?: string;

  // Relationships
  diamonds: Array<{
    diamond_id: string;
    quantity: number;
    original_weight?: number;
    final_weight?: number;
    notes?: string;
  }>;
}

const ManufacturingForm: React.FC<ManufacturingFormProps> = ({ manufacturing, onSuccess }) => {
  const isEditing = !!manufacturing;

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    watch,
    reset
  } = useForm<ManufacturingFormData>({
    defaultValues: manufacturing ? {
      vendor_id: manufacturing.vendor_id?.toString() || '',
      order_type: manufacturing.order_type || '',
      priority: manufacturing.priority || 'normal',
      description: manufacturing.description || '',
      special_instructions: manufacturing.special_instructions || '',
      sent_date: manufacturing.sent_date
        ? (manufacturing.sent_date.includes('T') ? manufacturing.sent_date.split('T')[0] : manufacturing.sent_date)
        : new Date().toISOString().split('T')[0],
      expected_return_date: manufacturing.expected_return_date
        ? (manufacturing.expected_return_date.includes('T') ? manufacturing.expected_return_date.split('T')[0] : manufacturing.expected_return_date)
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      actual_return_date: manufacturing.actual_return_date
        ? (manufacturing.actual_return_date.includes('T') ? manufacturing.actual_return_date.split('T')[0] : manufacturing.actual_return_date)
        : '',
      promised_delivery_date: manufacturing.promised_delivery_date
        ? (manufacturing.promised_delivery_date.includes('T') ? manufacturing.promised_delivery_date.split('T')[0] : manufacturing.promised_delivery_date)
        : '',
      status: manufacturing.status || 'draft',
      progress_percentage: manufacturing.progress_percentage || 0,
      quality_check_status: manufacturing.quality_check_status || '',
      quality_notes: manufacturing.quality_notes || '',
      inspector_name: manufacturing.inspector_name || '',
      inspection_date: manufacturing.inspection_date
        ? (manufacturing.inspection_date.includes('T') ? manufacturing.inspection_date.split('T')[0] : manufacturing.inspection_date)
        : '',
      estimated_cost: manufacturing.estimated_cost,
      actual_cost: manufacturing.actual_cost,
      advance_paid: manufacturing.advance_paid,
      payment_status: manufacturing.payment_status || 'pending',
      total_original_weight: manufacturing.total_original_weight,
      total_final_weight: manufacturing.total_final_weight,
      notes: manufacturing.notes || '',
      internal_notes: manufacturing.internal_notes || '',
      diamonds: manufacturing.diamonds?.map(d => ({
        diamond_id: d.diamond_id?.toString() || '',
        quantity: d.quantity || 1,
        original_weight: d.original_weight,
        final_weight: d.final_weight,
        notes: d.notes || ''
      })) || [{ diamond_id: '', quantity: 1 }]
    } : {
      sent_date: new Date().toISOString().split('T')[0],
      expected_return_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      priority: 'normal',
      status: 'draft',
      progress_percentage: 0,
      payment_status: 'pending',
      diamonds: [{ diamond_id: '', quantity: 1 }]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'diamonds'
  });

  const { data: vendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await api.vendors.list();
      return response?.data || [];
    }
  });

  const { data: diamonds, isLoading: isDiamondsLoading, error: diamondsError } = useQuery({
    queryKey: ['diamonds', { status: 'in_stock' }],
    queryFn: async () => {
      const response = await api.diamonds.list({ status: 'in_stock' });
      return response?.data || [];
    }
  });

  const mutation = useMutation({
    mutationFn: async (data: ManufacturingFormData) => {
      // Enhanced client-side validation
      const validationErrors = validateOrderData(data);
      const diamondErrors = validateDiamondSelections();

      const allErrors = [...validationErrors, ...diamondErrors];
      if (allErrors.length > 0) {
        throw new Error(allErrors.join(', '));
      }

      // Validate that all diamonds are selected and have quantities
      const invalidDiamonds = data.diamonds.filter(d => !d.diamond_id || !d.quantity || parseInt(d.quantity) < 1);
      if (invalidDiamonds.length > 0) {
        throw new Error('All diamonds must be selected with valid quantities');
      }

      // Prepare payload with proper data types
      const payload = {
        ...data,
        vendor_id: parseInt(data.vendor_id),
        progress_percentage: data.progress_percentage ? Number(data.progress_percentage) : undefined,
        estimated_cost: data.estimated_cost ? Number(data.estimated_cost) : undefined,
        actual_cost: data.actual_cost ? Number(data.actual_cost) : undefined,
        advance_paid: data.advance_paid ? Number(data.advance_paid) : undefined,
        total_original_weight: data.total_original_weight ? Number(data.total_original_weight) : undefined,
        total_final_weight: data.total_final_weight ? Number(data.total_final_weight) : undefined,
        diamonds: data.diamonds.map(d => ({
          diamond_id: parseInt(d.diamond_id),
          quantity: parseInt(d.quantity),
          original_weight: d.original_weight ? Number(d.original_weight) : undefined,
          final_weight: d.final_weight ? Number(d.final_weight) : undefined,
          notes: d.notes || undefined
        }))
      };

      if (isEditing) {
        return api.manufacturing.update(manufacturing!.id, payload);
      } else {
        return api.manufacturing.create(payload);
      }
    },
    onSuccess: () => {
      toast.success(`Manufacturing request ${isEditing ? 'updated' : 'created'} successfully`);
      onSuccess();
    },
    onError: (error: any) => {
      const message = error.message || error.response?.data?.message || `Failed to ${isEditing ? 'update' : 'create'} manufacturing request`;
      toast.error(message);
    }
  });

  const onSubmit = (data: ManufacturingFormData) => {
    mutation.mutate(data);
  };

  const watchedDiamonds = watch('diamonds');

  const vendorOptions = Array.isArray(vendors)
    ? vendors.map((vendor: any) => ({
        value: vendor.id.toString(),
        label: vendor.name
      }))
    : [];

  // Calculate remaining quantities for each diamond considering all selections
  const calculateRemainingQuantity = (diamondId: number, excludeIndex?: number) => {
    const diamond = diamonds?.find((d: any) => d.id === diamondId);
    if (!diamond) return 0;

    // Calculate total quantity already selected for this diamond (excluding current index)
    const totalSelected = watchedDiamonds.reduce((total, d, index) => {
      if (index === excludeIndex) return total; // Exclude current selection
      if (parseInt(d.diamond_id) === diamondId) {
        return total + (parseInt(d.quantity) || 0);
      }
      return total;
    }, 0);

    return Math.max(0, diamond.quantity - totalSelected);
  };

  // Validate diamond selections for duplicates and over-allocation
  const validateDiamondSelections = () => {
    const errors: string[] = [];
    const diamondUsage: Record<number, number> = {};

    watchedDiamonds.forEach((selection, index) => {
      const diamondId = parseInt(selection.diamond_id);
      const quantity = parseInt(selection.quantity) || 0;

      if (diamondId && quantity > 0) {
        diamondUsage[diamondId] = (diamondUsage[diamondId] || 0) + quantity;
      }
    });

    // Check for over-allocation
    Object.entries(diamondUsage).forEach(([diamondId, totalUsed]) => {
      const diamond = diamonds?.find((d: any) => d.id === parseInt(diamondId));
      if (diamond && totalUsed > diamond.quantity) {
        errors.push(`${diamond.shape} ${diamond.carat}ct: Using ${totalUsed} but only ${diamond.quantity} available`);
      }
    });

    return errors;
  };

  const getDiamondOptions = (currentIndex: number) => {
    if (!Array.isArray(diamonds)) return [];

    return diamonds
      .map((diamond: any) => {
        const remainingQty = calculateRemainingQuantity(diamond.id, currentIndex);
        return {
          diamond,
          remainingQty,
          value: diamond.id.toString(),
          label: `${diamond.shape} - ${diamond.carat}ct (${remainingQty} available)`
        };
      })
      .filter(option => option.remainingQty > 0) // Only show diamonds with available quantity
      .map(option => ({
        value: option.value,
        label: option.label
      }));
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      {/* Basic Information Section */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Vendor"
            options={vendorOptions}
            required
            {...register('vendor_id', { required: 'Vendor is required' })}
            error={errors.vendor_id?.message}
            helperText="Select the manufacturing vendor"
          />

          <Select
            label="Order Type"
            options={ORDER_TYPES}
            required
            {...register('order_type', { required: 'Order type is required' })}
            error={errors.order_type?.message}
            helperText="Type of manufacturing work"
          />

          <Select
            label="Priority"
            options={PRIORITIES}
            {...register('priority')}
            error={errors.priority?.message}
            helperText="Order priority level"
          />

          <Select
            label="Status"
            options={MANUFACTURING_STATUSES}
            {...register('status')}
            error={errors.status?.message}
            helperText="Current order status"
          />
        </div>
      </div>

      {/* Timeline Section */}
      <div className="bg-blue-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Timeline</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Sent Date"
            type="date"
            required
            {...register('sent_date', { required: 'Sent date is required' })}
            error={errors.sent_date?.message}
            helperText="Date when order was sent to vendor"
          />

          <Input
            label="Expected Return Date"
            type="date"
            required
            {...register('expected_return_date', { required: 'Expected return date is required' })}
            error={errors.expected_return_date?.message}
            helperText="Expected completion date"
          />

          <Input
            label="Actual Return Date"
            type="date"
            {...register('actual_return_date')}
            error={errors.actual_return_date?.message}
            helperText="Actual completion date"
          />

          <Input
            label="Promised Delivery Date"
            type="date"
            {...register('promised_delivery_date')}
            error={errors.promised_delivery_date?.message}
            helperText="Date promised to customer"
          />
        </div>
      </div>

      {/* Financial Section */}
      <div className="bg-red-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Estimated Cost"
            type="number"
            step="0.01"
            {...register('estimated_cost', {
              min: { value: 0, message: 'Cost cannot be negative' }
            })}
            error={errors.estimated_cost?.message}
            helperText="Estimated manufacturing cost"
          />

          <Input
            label="Actual Cost"
            type="number"
            step="0.01"
            {...register('actual_cost', {
              min: { value: 0, message: 'Cost cannot be negative' }
            })}
            error={errors.actual_cost?.message}
            helperText="Actual cost charged"
          />

          <Input
            label="Advance Paid"
            type="number"
            step="0.01"
            {...register('advance_paid', {
              min: { value: 0, message: 'Advance cannot be negative' }
            })}
            error={errors.advance_paid?.message}
            helperText="Advance payment made"
          />

          <Select
            label="Payment Status"
            options={PAYMENT_STATUSES}
            {...register('payment_status')}
            error={errors.payment_status?.message}
            helperText="Current payment status"
          />
        </div>
      </div>

      {/* Diamonds Section */}
      <div className="bg-purple-50 p-6 rounded-lg">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Diamonds</h3>
            {/* Summary of selected diamonds */}
            {watchedDiamonds.length > 0 && (
              <p className="text-sm text-gray-600 mt-1">
                {watchedDiamonds.filter(d => d.diamond_id).length} diamond(s) selected,
                Total quantity: {watchedDiamonds.reduce((sum, d) => sum + (parseInt(d.quantity) || 0), 0)}
              </p>
            )}
          </div>
          <Button
            type="button"
            variant="secondary"
            onClick={() => {
              // Check if there are any available diamonds before adding
              const availableDiamonds = getDiamondOptions(-1); // Use -1 to get all available
              if (availableDiamonds.length === 0) {
                toast.error('No more diamonds available for selection');
                return;
              }
              append({ diamond_id: '', quantity: 1 });
            }}
            disabled={getDiamondOptions(-1).length === 0}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Diamond
          </Button>
        </div>

        <div className="space-y-6">
          {fields.map((field, index) => {
            const selectedDiamond = Array.isArray(diamonds)
              ? diamonds.find((d: any) => d.id === parseInt(watchedDiamonds[index]?.diamond_id))
              : undefined;

            // Calculate remaining quantity for this specific diamond selection
            const remainingQuantity = selectedDiamond
              ? calculateRemainingQuantity(selectedDiamond.id, index)
              : 0;

            // Add current selection back to get max allowed for this field
            const currentQuantity = parseInt(watchedDiamonds[index]?.quantity) || 0;
            const maxAllowed = remainingQuantity + currentQuantity;

            return (
              <div key={field.id} className="border border-gray-200 rounded-lg p-4 bg-white">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-1">
                    <Select
                      label="Diamond"
                      options={getDiamondOptions(index)}
                      {...register(`diamonds.${index}.diamond_id`, { required: 'Diamond is required' })}
                      error={errors.diamonds?.[index]?.diamond_id?.message}
                    />
                  </div>

                  <div>
                    <Input
                      label={`Quantity ${selectedDiamond ? `(${maxAllowed} available)` : ''}`}
                      type="number"
                      min="1"
                      max={maxAllowed || 1}
                      {...register(`diamonds.${index}.quantity`, {
                        required: 'Quantity is required',
                        min: { value: 1, message: 'Minimum quantity is 1' },
                        max: {
                          value: maxAllowed || 1,
                          message: `Maximum available: ${maxAllowed || 1}`
                        },
                        validate: (value) => {
                          const qty = parseInt(value);
                          if (selectedDiamond && qty > maxAllowed) {
                            return `Only ${maxAllowed} available for this diamond`;
                          }
                          return true;
                        }
                      })}
                      error={errors.diamonds?.[index]?.quantity?.message}
                      helperText={selectedDiamond ? `${selectedDiamond.shape} ${selectedDiamond.carat}ct - Certificate: ${selectedDiamond.certificate_no}` : ''}
                    />
                  </div>

                  <div className="flex items-end">
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="danger"
                        onClick={() => remove(index)}
                        className="mb-1"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {/* Enhanced weight tracking for editing mode */}
                {isEditing && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-200">
                    <Input
                      label="Original Weight (ct)"
                      type="number"
                      step="0.001"
                      {...register(`diamonds.${index}.original_weight`, {
                        min: { value: 0.001, message: 'Weight must be positive' }
                      })}
                      error={errors.diamonds?.[index]?.original_weight?.message}
                      helperText="Original diamond weight"
                    />

                    <Input
                      label="Final Weight (ct)"
                      type="number"
                      step="0.001"
                      {...register(`diamonds.${index}.final_weight`, {
                        min: { value: 0.001, message: 'Weight must be positive' }
                      })}
                      error={errors.diamonds?.[index]?.final_weight?.message}
                      helperText="Final weight after manufacturing"
                    />

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Notes
                      </label>
                      <textarea
                        {...register(`diamonds.${index}.notes`)}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Notes for this diamond..."
                      />
                      {errors.diamonds?.[index]?.notes && (
                        <p className="mt-1 text-sm text-red-600">{errors.diamonds[index]?.notes?.message}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Additional Notes Section */}
      <div className="bg-indigo-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>
        <div className="grid grid-cols-1 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              General Notes
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="General notes about this manufacturing order..."
            />
            {errors.notes && (
              <p className="mt-1 text-sm text-red-600">{errors.notes.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Internal Notes
            </label>
            <textarea
              {...register('internal_notes')}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Internal notes (not shared with vendor)..."
            />
            {errors.internal_notes && (
              <p className="mt-1 text-sm text-red-600">{errors.internal_notes.message}</p>
            )}
          </div>
        </div>
      </div>

      {/* Validation Summary */}
      {(() => {
        const diamondErrors = validateDiamondSelections();
        const hasValidDiamonds = watchedDiamonds.some(d => d.diamond_id && parseInt(d.quantity) > 0);

        if (diamondErrors.length > 0 || !hasValidDiamonds) {
          return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-red-800 mb-2">⚠️ Validation Issues:</h4>
              <ul className="text-sm text-red-700 space-y-1">
                {!hasValidDiamonds && (
                  <li>• At least one diamond must be selected with a valid quantity</li>
                )}
                {diamondErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          );
        }

        // Show success summary
        const totalDiamonds = watchedDiamonds.filter(d => d.diamond_id).length;
        const totalQuantity = watchedDiamonds.reduce((sum, d) => sum + (parseInt(d.quantity) || 0), 0);

        if (totalDiamonds > 0) {
          return (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-green-800 mb-2">✅ Ready to Submit:</h4>
              <p className="text-sm text-green-700">
                {totalDiamonds} diamond(s) selected with total quantity of {totalQuantity}
              </p>
            </div>
          );
        }

        return null;
      })()}

      <div className="flex gap-4">
        <Button
          type="submit"
          disabled={mutation.isPending || validateDiamondSelections().length > 0 || !watchedDiamonds.some(d => d.diamond_id && parseInt(d.quantity) > 0)}
          className="flex-1"
        >
          {mutation.isPending
            ? (isEditing ? 'Updating...' : 'Creating...')
            : (isEditing ? 'Update Manufacturing Request' : 'Create Manufacturing Request')
          }
        </Button>
      </div>
    </form>
  );
};

export default ManufacturingForm;