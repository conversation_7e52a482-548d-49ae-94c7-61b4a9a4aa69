# 🧪 Frontend Diamond Selection Testing Guide

## 🎯 **Testing Objectives**
Verify that all diamond selection and quantity management fixes are working correctly in the manufacturing form.

## 🔧 **Pre-Testing Setup**
1. Ensure both servers are running:
   - Backend: `http://localhost:8000` 
   - Frontend: `http://localhost:5173`
2. Login with credentials: `<EMAIL>` / `<PERSON><PERSON>@109`

## 📋 **Test Scenarios**

### **Test 1: Basic Diamond Loading**
**URL:** `http://localhost:5173/manufacturing/new`

**Steps:**
1. Navigate to the manufacturing form
2. Scroll to the "Diamonds" section
3. Click on the diamond dropdown

**Expected Results:**
- ✅ Dropdown shows available diamonds
- ✅ Format: "Princess - 1.25ct (1 available)"
- ✅ Only diamonds with quantity > 0 appear
- ✅ No "Select an option" if diamonds are available

**Current Available Diamonds:**
- Princess - 1.25ct (1 available)
- Round - 1.25ct (3 available) 
- Round - 1.25ct (1 available)
- Round - 1.5ct (1 available)
- Round - 1.0ct (1 available)
- Round - 2.0ct (1 available)

---

### **Test 2: Quantity Validation**
**Steps:**
1. Select "Round - 1.25ct (3 available)"
2. Try entering quantity: 5 (more than available)
3. Try entering quantity: 3 (exactly available)
4. Try entering quantity: 2 (less than available)

**Expected Results:**
- ❌ Quantity 5: Should show validation error
- ✅ Quantity 3: Should be accepted
- ✅ Quantity 2: Should be accepted
- ✅ Label shows "Quantity (3 available)"
- ✅ Max attribute set to 3

---

### **Test 3: Multiple Diamond Selection**
**Steps:**
1. Select first diamond: "Round - 1.25ct (3 available)" with quantity 2
2. Click "Add Diamond"
3. Check second dropdown options
4. Select second diamond: "Princess - 1.25ct (1 available)" with quantity 1

**Expected Results:**
- ✅ First selection: Round shows "Quantity (3 available)"
- ✅ Second dropdown: Round now shows "(1 available)" (3-2=1)
- ✅ Second dropdown: Princess still shows "(1 available)"
- ✅ Summary shows: "2 diamond(s) selected, Total quantity: 3"

---

### **Test 4: Same Diamond Multiple Times**
**Steps:**
1. Select "Round - 1.25ct (3 available)" with quantity 1
2. Click "Add Diamond"
3. Select same "Round - 1.25ct" with quantity 1
4. Try to add third selection of same diamond with quantity 2

**Expected Results:**
- ✅ First selection: Shows "Quantity (3 available)"
- ✅ Second selection: Shows "Quantity (2 available)" (3-1=2)
- ❌ Third selection: Should show validation error (only 1 remaining)
- ✅ Form validation summary shows error

---

### **Test 5: Add Diamond Button Behavior**
**Steps:**
1. Select all available diamonds with their full quantities
2. Try to click "Add Diamond" button

**Expected Results:**
- ✅ Button becomes disabled when no diamonds available
- ✅ Clicking shows toast: "No more diamonds available for selection"
- ✅ Button re-enables when quantities are reduced

---

### **Test 6: Real-time Validation Summary**
**Steps:**
1. Add diamonds with valid quantities
2. Try over-allocating one diamond
3. Fix the over-allocation

**Expected Results:**
- ✅ Green summary: "Ready to Submit: X diamond(s) selected with total quantity of Y"
- ❌ Red summary: "Validation Issues: [specific errors]"
- ✅ Submit button disabled during validation errors
- ✅ Submit button enabled when validation passes

---

### **Test 7: Form Submission**
**Steps:**
1. Fill required fields:
   - Vendor: "Test Vendor"
   - Sent Date: "2025-07-21"
   - Expected Return Date: "2025-08-21"
2. Add valid diamond selections
3. Submit form

**Expected Results:**
- ✅ Form submits successfully
- ✅ Success toast appears
- ✅ Redirects to manufacturing list
- ✅ New request appears in list

---

### **Test 8: Edge Cases**
**Steps:**
1. Try submitting with no diamonds
2. Try submitting with diamond selected but quantity 0
3. Try removing all diamonds and re-adding

**Expected Results:**
- ❌ No diamonds: Validation error
- ❌ Quantity 0: Validation error
- ✅ Remove/re-add: Works correctly

---

## 🐛 **Common Issues to Check**

### **If Diamonds Don't Load:**
1. Check browser console for errors
2. Verify network tab shows successful API calls
3. Check if authentication token is valid

### **If Quantities Don't Update:**
1. Verify `calculateRemainingQuantity` function is working
2. Check if `watchedDiamonds` is updating correctly
3. Look for React state update issues

### **If Validation Doesn't Work:**
1. Check `validateDiamondSelections` function
2. Verify form validation summary is rendering
3. Check submit button disabled state

### **If Add Diamond Fails:**
1. Verify `getDiamondOptions(-1)` returns correct options
2. Check if button disabled state is correct
3. Look for toast error messages

---

## 🎯 **Success Criteria**

**All tests pass = Production Ready!**

The diamond selection system should:
- ✅ Prevent over-allocation of diamonds
- ✅ Show real-time availability updates
- ✅ Provide clear validation feedback
- ✅ Handle multiple diamond selections correctly
- ✅ Work seamlessly for real-world jewelry manufacturing

---

## 📞 **If Issues Found**

1. **Document the specific test that failed**
2. **Note the expected vs actual behavior**
3. **Check browser console for errors**
4. **Verify API responses in network tab**
5. **Report with screenshots if possible**

The system is designed to be production-ready for real jewelry manufacturing workflows!
