#!/usr/bin/env python3

import requests
import json

def test_diamond_selection_comprehensive():
    """Comprehensive test for diamond selection fixes"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        print('🔧 COMPREHENSIVE DIAMOND SELECTION TEST')
        print('=' * 60)
        
        # Get available diamonds
        response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
        if response.status_code == 200:
            data = response.json()
            diamonds = data.get('data', [])
            available_diamonds = [d for d in diamonds if d.get('quantity', 0) > 0]
            
            print(f'\n📊 INITIAL DIAMOND INVENTORY:')
            print(f'   Total available diamonds: {len(available_diamonds)}')
            for diamond in available_diamonds[:5]:  # Show first 5
                print(f'   • ID {diamond["id"]}: {diamond["shape"]} {diamond["carat"]}ct - Qty: {diamond["quantity"]}')
            
            if len(available_diamonds) >= 3:
                # Test Scenario 1: Single diamond selection
                print(f'\n🧪 TEST 1: Single Diamond Selection')
                diamond1 = available_diamonds[0]
                test_qty = min(2, diamond1["quantity"])
                
                manufacturing_data = {
                    'vendor_id': 1,
                    'sent_date': '2025-07-21',
                    'expected_return_date': '2025-08-21',
                    'order_type': 'Diamond Cutting',
                    'description': 'Test single diamond selection',
                    'diamonds': [{
                        'diamond_id': diamond1['id'],
                        'quantity': test_qty
                    }]
                }
                
                response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
                if response.status_code == 201:
                    print(f'   ✅ Created request with {test_qty} x Diamond ID {diamond1["id"]}')
                    
                    # Verify diamond quantity updated
                    response = requests.get(f'{BASE_URL}/diamonds/{diamond1["id"]}', headers=headers)
                    if response.status_code == 200:
                        updated_diamond = response.json()
                        new_qty = updated_diamond.get('quantity', 0)
                        expected_qty = diamond1["quantity"] - test_qty
                        print(f'   • Original quantity: {diamond1["quantity"]}')
                        print(f'   • Used quantity: {test_qty}')
                        print(f'   • Expected remaining: {expected_qty}')
                        print(f'   • Actual remaining: {new_qty}')
                        if new_qty == expected_qty:
                            print('   ✅ Quantity correctly updated')
                        else:
                            print('   ❌ Quantity update failed')
                else:
                    print(f'   ❌ Failed: {response.status_code} - {response.text}')
                
                # Test Scenario 2: Multiple diamonds
                print(f'\n🧪 TEST 2: Multiple Diamond Selection')
                
                # Get fresh diamond data
                response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
                if response.status_code == 200:
                    fresh_data = response.json()
                    fresh_diamonds = [d for d in fresh_data.get('data', []) if d.get('quantity', 0) > 0]
                    
                    if len(fresh_diamonds) >= 2:
                        diamond2 = fresh_diamonds[0]
                        diamond3 = fresh_diamonds[1]
                        
                        manufacturing_data = {
                            'vendor_id': 1,
                            'sent_date': '2025-07-21',
                            'expected_return_date': '2025-08-21',
                            'order_type': 'Diamond Polishing',
                            'description': 'Test multiple diamond selection',
                            'diamonds': [
                                {
                                    'diamond_id': diamond2['id'],
                                    'quantity': min(1, diamond2['quantity'])
                                },
                                {
                                    'diamond_id': diamond3['id'],
                                    'quantity': min(1, diamond3['quantity'])
                                }
                            ]
                        }
                        
                        response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
                        if response.status_code == 201:
                            print(f'   ✅ Created multi-diamond request')
                            print(f'   • Diamond {diamond2["id"]}: {diamond2["shape"]} {diamond2["carat"]}ct')
                            print(f'   • Diamond {diamond3["id"]}: {diamond3["shape"]} {diamond3["carat"]}ct')
                        else:
                            print(f'   ❌ Failed: {response.status_code} - {response.text}')
                
                # Test Scenario 3: Over-allocation (should fail)
                print(f'\n🧪 TEST 3: Over-allocation Validation')
                
                response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
                if response.status_code == 200:
                    fresh_data = response.json()
                    fresh_diamonds = [d for d in fresh_data.get('data', []) if d.get('quantity', 0) > 0]
                    
                    if fresh_diamonds:
                        test_diamond = fresh_diamonds[0]
                        over_qty = test_diamond['quantity'] + 5
                        
                        manufacturing_data = {
                            'vendor_id': 1,
                            'sent_date': '2025-07-21',
                            'expected_return_date': '2025-08-21',
                            'order_type': 'Diamond Cutting',
                            'description': 'Test over-allocation',
                            'diamonds': [{
                                'diamond_id': test_diamond['id'],
                                'quantity': over_qty
                            }]
                        }
                        
                        response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
                        if response.status_code == 400:
                            print(f'   ✅ Correctly rejected over-allocation')
                            print(f'   • Requested: {over_qty}, Available: {test_diamond["quantity"]}')
                        elif response.status_code == 201:
                            print(f'   ❌ Should have rejected over-allocation but accepted it')
                        else:
                            print(f'   ⚠️  Unexpected response: {response.status_code}')
                
                # Test Scenario 4: Same diamond multiple times (should accumulate)
                print(f'\n🧪 TEST 4: Same Diamond Multiple Selections')
                
                response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
                if response.status_code == 200:
                    fresh_data = response.json()
                    fresh_diamonds = [d for d in fresh_data.get('data', []) if d.get('quantity', 0) >= 3]
                    
                    if fresh_diamonds:
                        test_diamond = fresh_diamonds[0]
                        
                        manufacturing_data = {
                            'vendor_id': 1,
                            'sent_date': '2025-07-21',
                            'expected_return_date': '2025-08-21',
                            'order_type': 'Diamond Cutting',
                            'description': 'Test same diamond multiple times',
                            'diamonds': [
                                {
                                    'diamond_id': test_diamond['id'],
                                    'quantity': 1
                                },
                                {
                                    'diamond_id': test_diamond['id'],
                                    'quantity': 1
                                }
                            ]
                        }
                        
                        response = requests.post(f'{BASE_URL}/manufacturing', json=manufacturing_data, headers=headers)
                        if response.status_code == 201:
                            print(f'   ✅ Accepted same diamond multiple times (total: 2)')
                            
                            # Verify total deduction
                            response = requests.get(f'{BASE_URL}/diamonds/{test_diamond["id"]}', headers=headers)
                            if response.status_code == 200:
                                updated_diamond = response.json()
                                new_qty = updated_diamond.get('quantity', 0)
                                expected_qty = test_diamond["quantity"] - 2
                                print(f'   • Total deducted: 2, Expected remaining: {expected_qty}, Actual: {new_qty}')
                        else:
                            print(f'   ❌ Failed: {response.status_code} - {response.text}')
                
                print(f'\n🎯 FRONTEND FEATURES TO TEST:')
                print(f'   1. ✅ Diamond options should update in real-time')
                print(f'   2. ✅ Available quantity should show remaining after selections')
                print(f'   3. ✅ Add Diamond button should be disabled when no diamonds available')
                print(f'   4. ✅ Quantity validation should prevent over-allocation')
                print(f'   5. ✅ Form should show validation summary')
                print(f'   6. ✅ Submit button should be disabled with validation errors')
                
            else:
                print('   ⚠️  Need at least 3 diamonds for comprehensive testing')
                
        else:
            print(f'❌ Failed to get diamonds: {response.status_code}')
            
    else:
        print(f'❌ Login failed: {response.status_code}')

if __name__ == '__main__':
    test_diamond_selection_comprehensive()
