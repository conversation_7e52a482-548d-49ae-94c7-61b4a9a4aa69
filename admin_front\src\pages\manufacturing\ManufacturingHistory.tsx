import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Calendar, Eye } from 'lucide-react';
import { api } from '../../lib/api';
import { ManufacturingRequest } from '../../types';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Modal from '../../components/ui/Modal';
import { LoadingState, ErrorState, EmptyState } from '../../components/DataStates';
import ManufacturingDetails from './ManufacturingDetails';

const ManufacturingHistory: React.FC = () => {
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<ManufacturingRequest | null>(null);
  const [vendorFilter, setVendorFilter] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  const { data: history, isLoading, error, refetch } = useQuery({
    queryKey: ['manufacturing-history', {
      vendor: vendorFilter,
      date_from: dateFrom,
      date_to: dateTo
    }],
    queryFn: async (): Promise<ManufacturingRequest[]> => {
      const params: any = {};
      if (vendorFilter) params.vendor_id = vendorFilter;
      if (dateFrom) params.date_from = dateFrom;
      if (dateTo) params.date_to = dateTo;

      const response = await api.manufacturing.list(params);
      // Manufacturing API returns array directly, not wrapped in data property
      return Array.isArray(response) ? response : [];
    },
    retry: 1
  });

  const { data: vendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await api.vendors.list();
      // The API service already returns response.data, so we don't need to access .data again
      return Array.isArray(response) ? response : response?.data || [];
    }
  });

  const handleViewDetails = (request: ManufacturingRequest) => {
    setSelectedRequest(request);
    setIsDetailsModalOpen(true);
  };

  const vendorOptions = vendors?.map((vendor: any) => ({
    value: vendor.id,
    label: vendor.name
  })) || [];

  if (isLoading) {
    return <LoadingState message="Loading manufacturing history..." />;
  }

  if (error) {
    return (
      <ErrorState 
        title="Failed to load manufacturing history"
        onRetry={refetch}
      />
    );
  }

  if (!history || history.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Manufacturing History</h1>
          <p className="text-gray-600">View completed manufacturing requests</p>
        </div>
        
        <EmptyState
          title="No manufacturing history found"
          description="No completed manufacturing requests match your filters"
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Manufacturing History</h1>
        <p className="text-gray-600">View completed manufacturing requests</p>
      </div>

      {/* Filters */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Select
            options={vendorOptions}
            value={vendorFilter}
            onChange={(e) => setVendorFilter(e.target.value)}
            placeholder="Filter by vendor"
          />
          <Input
            type="date"
            placeholder="From date"
            value={dateFrom}
            onChange={(e) => setDateFrom(e.target.value)}
          />
          <Input
            type="date"
            placeholder="To date"
            value={dateTo}
            onChange={(e) => setDateTo(e.target.value)}
          />
          <Button
            variant="ghost"
            onClick={() => {
              setVendorFilter('');
              setDateFrom('');
              setDateTo('');
            }}
          >
            Clear Filters
          </Button>
        </div>
      </Card>

      {/* History List */}
      <Card padding={false}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Request Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vendor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Dates
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Diamonds
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {history?.map((request) => (
                <tr key={request.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        Request #{request.id.toString().slice(-8)}
                      </div>
                      <div className="text-sm text-gray-500">
                        Completed
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">{request.vendor?.name}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm text-gray-900">
                        Sent: {new Date(request.sent_date).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        Expected: {new Date(request.expected_return_date).toLocaleDateString()}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">
                      {request.diamonds.length} diamond(s)
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewDetails(request)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {history?.length === 0 && (
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-gray-500">No completed manufacturing requests found.</p>
            </div>
          )}
        </div>
      </Card>

      {/* Modal */}
      <Modal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedRequest(null);
        }}
        title="Manufacturing Request Details"
        size="lg"
      >
        {selectedRequest && (
          <ManufacturingDetails request={selectedRequest} />
        )}
      </Modal>
    </div>
  );
};

export default ManufacturingHistory;