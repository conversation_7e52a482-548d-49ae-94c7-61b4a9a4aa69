#!/usr/bin/env python3

import requests
import json

def check_manufacturing_statuses():
    """Check manufacturing request statuses"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # Get manufacturing requests and check statuses
        response = requests.get(f'{BASE_URL}/manufacturing', headers=headers)
        if response.status_code == 200:
            requests_data = response.json()
            print('Manufacturing Request Statuses:')
            status_counts = {}
            for req in requests_data:
                status = req['status']
                status_counts[status] = status_counts.get(status, 0) + 1
                print(f'  ID {req["id"]}: {status}')
            
            print(f'\nStatus Summary:')
            for status, count in status_counts.items():
                print(f'  {status}: {count}')
            
            print(f'\nDashboard shows open_manufacturing: 3')
            print(f'Actual "open" status count: {status_counts.get("open", 0)}')
            print(f'Total manufacturing requests: {len(requests_data)}')
            
            # Check if dashboard count matches open status
            open_count = status_counts.get("open", 0)
            if open_count == 3:
                print('✅ Dashboard count matches "open" status count')
            else:
                print('❌ Dashboard count does not match "open" status count')
                print('   This suggests the dashboard might be counting differently')
        else:
            print(f'Failed to get manufacturing requests: {response.status_code}')
    else:
        print(f'Login failed: {response.status_code}')

if __name__ == '__main__':
    check_manufacturing_statuses()
