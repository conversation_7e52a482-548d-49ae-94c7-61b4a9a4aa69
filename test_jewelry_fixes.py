#!/usr/bin/env python3

import requests
import json
import os

def test_jewelry_fixes():
    """Test all jewelry fixes after implementation"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        print('🔧 TESTING JEWELRY FIXES')
        print('=' * 50)
        
        # Test 1: Jewelry List API with Enhanced Data
        print('\n1. Testing Enhanced Jewelry List API...')
        response = requests.get(f'{BASE_URL}/jewelry', headers=headers)
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            jewelry_data = response.json()
            print(f'   Response format: {type(jewelry_data)}')
            
            if isinstance(jewelry_data, dict) and 'data' in jewelry_data:
                jewelry_items = jewelry_data['data']
                print(f'   ✅ Paginated response with {len(jewelry_items)} items')
                
                if jewelry_items:
                    sample = jewelry_items[0]
                    print(f'   Sample jewelry enhanced data:')
                    print(f'     • ID: {sample.get("id")}')
                    print(f'     • Name: {sample.get("name")}')
                    print(f'     • Image Path: {sample.get("image_path", "No image")}')
                    print(f'     • Diamonds: {len(sample.get("diamonds", []))}')
                    
                    # Check diamond details
                    diamonds = sample.get("diamonds", [])
                    if diamonds:
                        diamond_sample = diamonds[0]
                        print(f'     • Diamond details included: {diamond_sample.get("diamond") is not None}')
                        if diamond_sample.get("diamond"):
                            diamond_info = diamond_sample["diamond"]
                            print(f'     • Diamond: {diamond_info.get("shape")} {diamond_info.get("carat")}ct')
                    else:
                        print(f'     • No diamonds in this jewelry item')
            else:
                print(f'   ❌ Unexpected response format')
        else:
            print(f'   ❌ Failed: {response.text}')
        
        # Test 2: Create Jewelry with Image
        print('\n2. Testing Create Jewelry with Image...')
        
        # Get vendors and diamonds first
        vendors_response = requests.get(f'{BASE_URL}/vendors', headers=headers)
        diamonds_response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
        
        if vendors_response.status_code == 200 and diamonds_response.status_code == 200:
            vendors = vendors_response.json()
            diamonds_data = diamonds_response.json()
            diamonds = diamonds_data.get('data', [])
            available_diamonds = [d for d in diamonds if d.get('quantity', 0) > 0]
            
            if vendors and available_diamonds:
                # Create jewelry
                jewelry_data = {
                    'name': 'Test Jewelry with Image',
                    'design_code': f'IMG{len(jewelry_items) + 1:04d}',
                    'vendor_id': vendors[0]['id'],
                    'gross_weight': 12.5,
                    'metal_type': 'Gold',
                    'received_date': '2025-07-21',
                    'status': 'in_stock',
                    'diamonds': [{
                        'diamond_id': available_diamonds[0]['id'],
                        'quantity': 1
                    }]
                }
                
                response = requests.post(f'{BASE_URL}/jewelry', json=jewelry_data, headers=headers)
                print(f'   Create Status: {response.status_code}')
                
                if response.status_code == 201:
                    new_jewelry = response.json()
                    jewelry_id = new_jewelry.get('id')
                    print(f'   ✅ Created jewelry: ID {jewelry_id}')
                    
                    # Test image upload
                    print('\n3. Testing Image Upload...')
                    try:
                        # Create a simple test image
                        from PIL import Image
                        test_image_path = 'test_jewelry.jpg'
                        img = Image.new('RGB', (200, 200), color='blue')
                        img.save(test_image_path, 'JPEG')
                        
                        with open(test_image_path, 'rb') as f:
                            files = {'image': ('test_jewelry.jpg', f, 'image/jpeg')}
                            response = requests.post(f'{BASE_URL}/jewelry/{jewelry_id}/image', 
                                                   files=files, headers=headers)
                        
                        print(f'   Upload Status: {response.status_code}')
                        if response.status_code == 200:
                            print(f'   ✅ Image uploaded successfully')
                            
                            # Verify image in jewelry details
                            response = requests.get(f'{BASE_URL}/jewelry/{jewelry_id}', headers=headers)
                            if response.status_code == 200:
                                updated_jewelry = response.json()
                                image_path = updated_jewelry.get('image_path')
                                print(f'   • Image path saved: {image_path}')
                                if image_path:
                                    print(f'   ✅ Image path correctly saved in database')
                                else:
                                    print(f'   ❌ Image path not saved')
                        else:
                            print(f'   ❌ Image upload failed: {response.text}')
                            
                    except ImportError:
                        print('   ⚠️  PIL not available, skipping image test')
                    except Exception as e:
                        print(f'   ❌ Image test failed: {e}')
                    finally:
                        if os.path.exists(test_image_path):
                            os.remove(test_image_path)
                    
                    # Test 4: Update Jewelry
                    print('\n4. Testing Update Jewelry...')
                    update_data = {
                        'name': 'Updated Test Jewelry',
                        'design_code': jewelry_data['design_code'],
                        'vendor_id': jewelry_data['vendor_id'],
                        'gross_weight': 13.0,
                        'metal_type': 'White Gold',
                        'status': 'in_stock'
                    }
                    
                    response = requests.put(f'{BASE_URL}/jewelry/{jewelry_id}', 
                                          json=update_data, headers=headers)
                    print(f'   Update Status: {response.status_code}')
                    if response.status_code == 200:
                        updated_jewelry = response.json()
                        print(f'   ✅ Jewelry updated successfully')
                        print(f'   • New name: {updated_jewelry.get("name")}')
                        print(f'   • New weight: {updated_jewelry.get("gross_weight")}')
                        print(f'   • Image preserved: {updated_jewelry.get("image_path") is not None}')
                    else:
                        print(f'   ❌ Update failed: {response.text}')
                
                else:
                    print(f'   ❌ Create failed: {response.text}')
        
        print('\n🎯 FRONTEND TESTING CHECKLIST:')
        print('   1. Navigate to http://localhost:5173/jewelry')
        print('   2. ✅ Check if jewelry list loads properly')
        print('   3. ✅ Check if images display correctly')
        print('   4. ✅ Test "Add New Jewelry" form')
        print('   5. ✅ Test image upload functionality')
        print('   6. ✅ Test "Edit" functionality')
        print('   7. ✅ Test "View Details" modal')
        print('   8. ✅ Test diamond selection in form')
        
        print('\n🔧 EXPECTED RESULTS:')
        print('   • Jewelry list shows all items with images')
        print('   • Image upload works and displays immediately')
        print('   • Edit form pre-populates with existing data')
        print('   • Jewelry details show complete information')
        print('   • Diamond selection works with available diamonds')
        print('   • All CRUD operations work smoothly')
        
    else:
        print(f'❌ Login failed: {response.status_code}')

if __name__ == '__main__':
    test_jewelry_fixes()
