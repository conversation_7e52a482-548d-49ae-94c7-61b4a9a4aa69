{"data_mtime": 1753099813, "dep_lines": [2, 4, 5, 1, 3, 6, 7, 8, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["werkzeug.exceptions", "sqlalchemy.exc", "flask_jwt_extended.exceptions", "flask", "marshmallow", "traceback", "jwt", "functools", "app", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "33ac6cedc85c9ab0ba1ac2deb7c847413f69cab0", "id": "app.utils.error_handler", "ignore_all": true, "interface_hash": "d81b339b5954d74c89b9459ab68948a9076c48a8", "mtime": 1753075556, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\utils\\error_handler.py", "plugin_data": null, "size": 4926, "suppressed": [], "version_id": "1.15.0"}