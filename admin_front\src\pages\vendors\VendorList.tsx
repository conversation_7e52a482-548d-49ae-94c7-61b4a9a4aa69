import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { api } from '../../lib/api';
import { Vendor } from '../../types';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Modal from '../../components/ui/Modal';
import { LoadingState, ErrorState, EmptyState } from '../../components/DataStates';
import VendorForm from './VendorForm';
import toast from 'react-hot-toast';

const VendorList: React.FC = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const queryClient = useQueryClient();

  const {
    data: vendors,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['vendors', { search: searchTerm }],
    queryFn: async (): Promise<Vendor[]> => {
      const params: any = {};
      if (searchTerm) params.search = searchTerm;

      const response = await api.vendors.list(params);
      // The API service already returns response.data, so we don't need to access .data again
      return Array.isArray(response) ? response : response?.data || [];
    },
    retry: 1
  });

  // Enhanced error handling for API calls
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await api.vendors.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendors'] });
      toast.success('Vendor deleted successfully');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to delete vendor';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  const handleEdit = (vendor: Vendor) => {
    setSelectedVendor(vendor);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (vendor: Vendor) => {
    if (window.confirm('Are you sure you want to delete this vendor? This action cannot be undone.')) {
      deleteMutation.mutate(vendor.id);
    }
  };

  if (isLoading) {
    return <LoadingState message="Loading vendors..." />;
  }

  if (error) {
    return (
      <ErrorState
        title="Failed to load vendors"
        description="Unable to load vendor list. Please check your connection and try again."
        onRetry={refetch}
        error={error as Error}
      />
    );
  }

  const filteredVendors = vendors || [];

  // Refactored table row rendering logic
  const renderTableRows = (vendors: Vendor[]) => (
    vendors.map((vendor) => (
      <tr key={vendor.id} className="hover:bg-gray-50">
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm font-medium text-gray-900">{vendor.name}</div>
            <div className="text-sm text-gray-500">
              GST: {vendor.gst_number}
            </div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="text-sm font-mono text-gray-900">{vendor.gst_number}</span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="text-sm text-gray-900">{vendor.contact_number}</span>
        </td>
        <td className="px-6 py-4">
          <div className="text-sm text-gray-900 max-w-xs truncate" title={vendor.address}>
            {vendor.address}
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(vendor)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(vendor)}
            >
              <Trash2 className="h-4 w-4 text-red-600" />
            </Button>
          </div>
        </td>
      </tr>
    ))
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vendors</h1>
          <p className="text-gray-600">Manage your suppliers and manufacturing partners</p>
        </div>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Vendor
        </Button>
      </div>

      {/* Search */}
      <Card>
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <Input
              placeholder="Search vendors by name, GST number, or contact..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <Button
            variant="ghost"
            onClick={() => setSearchTerm('')}
          >
            Clear
          </Button>
        </div>
      </Card>

      {/* Vendor List */}
      {filteredVendors.length === 0 ? (
        <EmptyState
          title="No vendors found"
          description={
            searchTerm
              ? "No vendors match your search criteria. Try adjusting your search term."
              : "You haven't added any vendors yet. Start by adding your first vendor."
          }
          action={{
            label: "Add Vendor",
            onClick: () => setIsAddModalOpen(true)
          }}
        />
      ) : (
        <Card padding={false}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vendor Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  GST Number
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Address
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {renderTableRows(filteredVendors)}
            </tbody>
          </table>
        </div>
      </Card>
      )}

      {/* Modals */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New Vendor"
        size="lg"
      >
        <VendorForm
          onSuccess={() => {
            setIsAddModalOpen(false);
            queryClient.invalidateQueries({ queryKey: ['vendors'] });
          }}
        />
      </Modal>

      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedVendor(null);
        }}
        title="Edit Vendor"
        size="lg"
      >
        {selectedVendor && (
          <VendorForm
            vendor={selectedVendor}
            onSuccess={() => {
              setIsEditModalOpen(false);
              setSelectedVendor(null);
              queryClient.invalidateQueries({ queryKey: ['vendors'] });
            }}
          />
        )}
      </Modal>
    </div>
  );
};

export default VendorList;