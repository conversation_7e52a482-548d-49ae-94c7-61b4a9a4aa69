{"data_mtime": 1753261112, "dep_lines": [124, 3, 4, 5, 124, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["PIL.Image", "requests", "json", "os", "PIL", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "http", "http.cookiejar", "io", "requests.auth", "requests.models", "types", "typing"], "hash": "684bb301646b6f794ff55e71245049739c0abd05", "id": "test_jewelry_comprehensive", "ignore_all": false, "interface_hash": "f92357370a249174d91939ec34c2cb218b5c8f5e", "mtime": 1753261106, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\test_jewelry_comprehensive.py", "plugin_data": null, "size": 9823, "suppressed": [], "version_id": "1.15.0"}