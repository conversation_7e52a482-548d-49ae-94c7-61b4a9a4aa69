#!/usr/bin/env python3

import requests
import json
import os

def test_jewelry_comprehensive():
    """Comprehensive test of jewelry functionality"""
    
    BASE_URL = 'http://localhost:8000/api'

    # Login first
    login_data = {'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'}
    response = requests.post(f'{BASE_URL}/auth/login', json=login_data)
    if response.status_code == 200:
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        print('🔍 COMPREHENSIVE JEWELRY SYSTEM TEST')
        print('=' * 60)
        
        # Test 1: Jewelry List API
        print('\n1. Testing Jewelry List API...')
        response = requests.get(f'{BASE_URL}/jewelry', headers=headers)
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            jewelry_data = response.json()
            print(f'   Response type: {type(jewelry_data)}')
            
            if isinstance(jewelry_data, list):
                print(f'   Total jewelry items: {len(jewelry_data)}')
                if jewelry_data:
                    sample = jewelry_data[0]
                    print(f'   Sample jewelry keys: {list(sample.keys())}')
                    print(f'   Sample jewelry:')
                    print(f'     • ID: {sample.get("id")}')
                    print(f'     • Name: {sample.get("name")}')
                    print(f'     • Design Code: {sample.get("design_code")}')
                    print(f'     • Status: {sample.get("status")}')
                    print(f'     • Image Path: {sample.get("image_path")}')
                    print(f'     • Vendor: {sample.get("vendor", {}).get("name", "N/A")}')
                    print(f'     • Diamonds: {len(sample.get("diamonds", []))}')
                    
                    # Check diamond details in jewelry
                    diamonds = sample.get("diamonds", [])
                    if diamonds:
                        diamond_sample = diamonds[0]
                        print(f'     • Diamond sample keys: {list(diamond_sample.keys())}')
                        print(f'     • Diamond details: {diamond_sample.get("diamond", "No details")}')
            else:
                print(f'   Unexpected response format: {jewelry_data}')
        else:
            print(f'   ❌ Failed: {response.text}')
        
        # Test 2: Jewelry Details API
        if jewelry_data and len(jewelry_data) > 0:
            print('\n2. Testing Jewelry Details API...')
            test_jewelry_id = jewelry_data[0]['id']
            response = requests.get(f'{BASE_URL}/jewelry/{test_jewelry_id}', headers=headers)
            print(f'   Status: {response.status_code}')
            if response.status_code == 200:
                jewelry_detail = response.json()
                print(f'   ✅ Jewelry details retrieved')
                print(f'   • Name: {jewelry_detail.get("name")}')
                print(f'   • Image Path: {jewelry_detail.get("image_path")}')
                print(f'   • Diamonds: {len(jewelry_detail.get("diamonds", []))}')
            else:
                print(f'   ❌ Failed: {response.text}')
        
        # Test 3: Vendors API (needed for jewelry form)
        print('\n3. Testing Vendors API...')
        response = requests.get(f'{BASE_URL}/vendors', headers=headers)
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            vendors = response.json()
            print(f'   ✅ Vendors available: {len(vendors)}')
            if vendors:
                print(f'   • Sample vendor: ID {vendors[0]["id"]}, Name: "{vendors[0]["name"]}"')
        else:
            print(f'   ❌ Failed: {response.text}')
        
        # Test 4: Diamonds API (needed for jewelry form)
        print('\n4. Testing Diamonds API...')
        response = requests.get(f'{BASE_URL}/diamonds?status=in_stock', headers=headers)
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            diamonds_data = response.json()
            diamonds = diamonds_data.get('data', [])
            available_diamonds = [d for d in diamonds if d.get('quantity', 0) > 0]
            print(f'   ✅ Available diamonds: {len(available_diamonds)}')
        else:
            print(f'   ❌ Failed: {response.text}')
        
        # Test 5: Create Jewelry API
        print('\n5. Testing Create Jewelry API...')
        if vendors and available_diamonds:
            test_jewelry_data = {
                'name': 'Test Jewelry Item',
                'design_code': f'TEST{len(jewelry_data) + 1:04d}',
                'vendor_id': vendors[0]['id'],
                'gross_weight': 15.5,
                'metal_type': 'Gold',
                'received_date': '2025-07-21',
                'status': 'in_stock',
                'diamonds': [{
                    'diamond_id': available_diamonds[0]['id'],
                    'quantity': 1
                }]
            }
            
            response = requests.post(f'{BASE_URL}/jewelry', json=test_jewelry_data, headers=headers)
            print(f'   Status: {response.status_code}')
            if response.status_code == 201:
                new_jewelry = response.json()
                print(f'   ✅ Created jewelry: ID {new_jewelry.get("id")}')
                test_jewelry_id = new_jewelry.get("id")
                
                # Test 6: Image Upload
                print('\n6. Testing Image Upload...')
                # Create a simple test image file
                test_image_path = 'test_jewelry_image.jpg'
                try:
                    # Create a simple test image (1x1 pixel)
                    from PIL import Image
                    img = Image.new('RGB', (100, 100), color='red')
                    img.save(test_image_path, 'JPEG')
                    
                    with open(test_image_path, 'rb') as f:
                        files = {'image': ('test.jpg', f, 'image/jpeg')}
                        response = requests.post(f'{BASE_URL}/jewelry/{test_jewelry_id}/image', 
                                               files=files, headers=headers)
                    
                    print(f'   Status: {response.status_code}')
                    if response.status_code == 200:
                        print(f'   ✅ Image uploaded successfully')
                        
                        # Verify image was saved
                        response = requests.get(f'{BASE_URL}/jewelry/{test_jewelry_id}', headers=headers)
                        if response.status_code == 200:
                            updated_jewelry = response.json()
                            image_path = updated_jewelry.get('image_path')
                            print(f'   • Image path: {image_path}')
                            if image_path:
                                print(f'   ✅ Image path saved in database')
                            else:
                                print(f'   ❌ Image path not saved')
                    else:
                        print(f'   ❌ Image upload failed: {response.text}')
                        
                except ImportError:
                    print('   ⚠️  PIL not available, skipping image upload test')
                except Exception as e:
                    print(f'   ❌ Image upload test failed: {e}')
                finally:
                    # Clean up test image
                    if os.path.exists(test_image_path):
                        os.remove(test_image_path)
                
                # Test 7: Update Jewelry
                print('\n7. Testing Update Jewelry...')
                update_data = {
                    'name': 'Updated Test Jewelry',
                    'design_code': test_jewelry_data['design_code'],
                    'vendor_id': test_jewelry_data['vendor_id'],
                    'gross_weight': 16.0,
                    'metal_type': 'White Gold',
                    'status': 'in_stock'
                }
                
                response = requests.put(f'{BASE_URL}/jewelry/{test_jewelry_id}', 
                                      json=update_data, headers=headers)
                print(f'   Status: {response.status_code}')
                if response.status_code == 200:
                    updated_jewelry = response.json()
                    print(f'   ✅ Jewelry updated successfully')
                    print(f'   • New name: {updated_jewelry.get("name")}')
                    print(f'   • New weight: {updated_jewelry.get("gross_weight")}')
                else:
                    print(f'   ❌ Update failed: {response.text}')
                
            else:
                print(f'   ❌ Create failed: {response.text}')
        
        print('\n🎯 FRONTEND ISSUES TO CHECK:')
        print('   1. ❌ Image upload not working - check API endpoint and file handling')
        print('   2. ❌ Jewelry details not showing - check data parsing and display')
        print('   3. ❌ Edit jewelry not working - check form submission and API calls')
        print('   4. ❌ Image display not working - check image path and serving')
        
        print('\n🔧 SPECIFIC AREAS TO INVESTIGATE:')
        print('   1. Image upload API endpoint and file handling')
        print('   2. Frontend image display logic and path resolution')
        print('   3. Jewelry form data binding and submission')
        print('   4. Jewelry details modal data display')
        print('   5. Edit functionality and form pre-population')
        
    else:
        print(f'❌ Login failed: {response.status_code}')

if __name__ == '__main__':
    test_jewelry_comprehensive()
